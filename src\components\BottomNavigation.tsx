import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, User, Car } from "lucide-react";
import { cn } from "@/lib/utils";

interface BottomNavigationProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

const tabs = [
  { id: 'map', icon: Map, label: 'Carte', color: 'text-blue-600' },
  { id: 'courses', icon: Car, label: 'Courses', color: 'text-yellow-600' },
  { id: 'ai', icon: Bot, label: 'IA', color: 'text-purple-600' },
  { id: 'earnings', icon: TrendingUp, label: 'Revenus', color: 'text-green-600' },
  { id: 'profile', icon: User, label: 'Profil', color: 'text-gray-600' },
];

export default function BottomNavigation({ activeTab, onTabChange }: BottomNavigationProps) {
  return (
    <div className="fixed bottom-0 left-0 right-0 nav-uber safe-area-bottom z-50">
      <div className="flex justify-around items-center py-3 px-2 max-w-md mx-auto">
        {tabs.map((tab) => {
          const Icon = tab.icon;
          const isActive = activeTab === tab.id;

          return (
            <button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={cn(
                "nav-item-uber relative",
                isActive && "active"
              )}
            >
              {/* Active indicator */}
              {isActive && (
                <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-yellow-500 rounded-full"></div>
              )}

              <Icon
                size={22}
                className={cn(
                  "mb-1 transition-all duration-300",
                  isActive
                    ? "text-yellow-600 scale-110"
                    : "text-gray-500"
                )}
              />
              <span className={cn(
                "text-xs font-medium transition-all duration-300",
                isActive
                  ? "text-yellow-600 font-semibold"
                  : "text-gray-500"
              )}>
                {tab.label}
              </span>

              {/* Ripple effect on tap */}
              <div className="absolute inset-0 rounded-lg overflow-hidden">
                <div className="absolute inset-0 bg-yellow-400 opacity-0 scale-0 rounded-lg transition-all duration-300 active:opacity-20 active:scale-100"></div>
              </div>
            </button>
          );
        })}
      </div>
    </div>
  );
}