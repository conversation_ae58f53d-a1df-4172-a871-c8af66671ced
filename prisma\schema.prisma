// ChauffeurX VTC Driver App Database Schema
// Designed for Abidjan, Ivory Coast market

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Authentication and User Management
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String?   @unique
  emailVerified DateTime?
  image         String?
  accounts      Account[]
  sessions      Session[]

  // Link to driver profile
  driver        Driver?
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// Driver-specific models
model Driver {
  id                String   @id @default(cuid())
  userId            String   @unique
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Personal Information
  firstName         String
  lastName          String
  phone             String   @unique
  profileImage      String?

  // Vehicle Information
  vehicleMake       String
  vehicleModel      String
  vehicleYear       Int
  vehicleColor      String
  licensePlate      String   @unique

  // Driver Credentials
  licenseNumber     String   @unique
  licenseExpiry     DateTime

  // App Settings
  preferredZones    String[] // Array of zone names
  isActive          Boolean  @default(true)
  isOnline          Boolean  @default(false)

  // Statistics
  totalRides        Int      @default(0)
  totalEarnings     Float    @default(0)
  averageRating     Float    @default(0)

  // Timestamps
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  lastActiveAt      DateTime @default(now())

  // Relations
  rides             Ride[]
  earnings          Earning[]
  preferences       DriverPreference?
}

model DriverPreference {
  id                String  @id @default(cuid())
  driverId          String  @unique
  driver            Driver  @relation(fields: [driverId], references: [id], onDelete: Cascade)

  // Notification Settings
  enablePushNotifications    Boolean @default(true)
  enableHotZoneAlerts       Boolean @default(true)
  enableWeatherAlerts       Boolean @default(true)
  enableEarningsReminders   Boolean @default(true)

  // App Settings
  preferredLanguage         String  @default("fr")
  darkMode                  Boolean @default(false)
  autoRefreshInterval       Int     @default(300) // seconds

  // Working Preferences
  preferredWorkingHours     String? // JSON string for time ranges
  maxDistanceToHotZone      Float   @default(10.0) // kilometers

  createdAt                 DateTime @default(now())
  updatedAt                 DateTime @updatedAt
}

// Ride Management
model Ride {
  id                String   @id @default(cuid())
  driverId          String
  driver            Driver   @relation(fields: [driverId], references: [id], onDelete: Cascade)

  // Ride Details
  startLocation     String
  endLocation       String?
  startLatitude     Float
  startLongitude    Float
  endLatitude       Float?
  endLongitude      Float?

  // Timing
  startTime         DateTime
  endTime           DateTime?
  duration          Int?     // minutes

  // Financial
  estimatedFare     Float?
  actualFare        Float?
  currency          String   @default("CFA")

  // Ride Status
  status            RideStatus @default(ACTIVE)
  distance          Float?   // kilometers

  // Metadata
  weatherCondition  String?
  hotZoneAtStart    String?

  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
}

enum RideStatus {
  ACTIVE
  COMPLETED
  CANCELLED
}

// Earnings Tracking
model Earning {
  id                String   @id @default(cuid())
  driverId          String
  driver            Driver   @relation(fields: [driverId], references: [id], onDelete: Cascade)

  // Earning Details
  amount            Float
  currency          String   @default("CFA")
  source            EarningSource

  // Time Period
  date              DateTime
  weekNumber        Int
  monthNumber       Int
  year              Int

  // Metadata
  hoursWorked       Float?
  ridesCompleted    Int?
  averagePerRide    Float?

  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@index([driverId, date])
  @@index([driverId, year, monthNumber])
}

enum EarningSource {
  RIDE_FARE
  TIP
  BONUS
  INCENTIVE
}

// Hot Zone Analytics
model HotZone {
  id                String   @id @default(cuid())
  name              String   @unique

  // Geographic Data
  latitude          Float
  longitude         Float
  radius            Float    @default(1.0) // kilometers

  // Zone Information
  description       String?
  category          ZoneCategory

  // Analytics
  averageDemand     Float    @default(0)
  peakHours         String[] // Array of hour ranges
  averageWaitTime   Int      @default(10) // minutes
  averageFare       Float    @default(0)

  // Status
  isActive          Boolean  @default(true)

  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  analytics         ZoneAnalytics[]
}

enum ZoneCategory {
  BUSINESS
  RESIDENTIAL
  COMMERCIAL
  TRANSPORT_HUB
  ENTERTAINMENT
  HOSPITAL
  SCHOOL
  MARKET
}

// Real-time Zone Analytics
model ZoneAnalytics {
  id                String   @id @default(cuid())
  hotZoneId         String
  hotZone           HotZone  @relation(fields: [hotZoneId], references: [id], onDelete: Cascade)

  // Time-based Data
  timestamp         DateTime
  hour              Int
  dayOfWeek         Int

  // Demand Metrics
  demandLevel       Int      // 0-100
  activeDrivers     Int      @default(0)
  estimatedWaitTime Int      // minutes

  // Weather Impact
  weatherCondition  String?
  temperature       Float?

  // Events
  specialEvent      String?
  eventImpact       Float?   // multiplier

  createdAt         DateTime @default(now())

  @@index([hotZoneId, timestamp])
  @@index([timestamp])
}
