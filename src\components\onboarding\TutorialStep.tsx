import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ChevronLeft, ChevronRight, Map, Bot, TrendingUp, Bell, User, CheckCircle } from "lucide-react";

interface TutorialStepProps {
  onComplete: () => void;
}

const tutorialSteps = [
  {
    id: 1,
    icon: Map,
    title: "Carte interactive",
    description: "Visualisez les zones chaudes en temps réel sur la carte d'Abidjan",
    features: [
      "Zones colorées selon la demande",
      "Navigation GPS intégrée",
      "Informations météo en direct",
      "Position en temps réel"
    ]
  },
  {
    id: 2,
    icon: Bo<PERSON>,
    title: "Assistant IA",
    description: "Recevez des recommandations personnalisées basées sur l'IA",
    features: [
      "Suggestions de zones optimales",
      "Prédictions de demande",
      "Conseils personnalisés",
      "Analyse des tendances"
    ]
  },
  {
    id: 3,
    icon: TrendingUp,
    title: "Suivi des revenus",
    description: "Analysez vos performances et optimisez vos gains",
    features: [
      "Revenus en temps réel",
      "Statistiques détaillées",
      "Graphiques de performance",
      "Objectifs personnalisés"
    ]
  },
  {
    id: 4,
    icon: Bell,
    title: "Alertes intelligentes",
    description: "Restez informé des opportunités en temps réel",
    features: [
      "Notifications zones chaudes",
      "Alertes météo",
      "Événements spéciaux",
      "Rappels personnalisés"
    ]
  }
];

export default function TutorialStep({ onComplete }: TutorialStepProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [completed, setCompleted] = useState(false);

  const handleNext = () => {
    if (currentStep < tutorialSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      setCompleted(true);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = () => {
    onComplete();
  };

  if (completed) {
    return (
      <div className="text-center space-y-6">
        <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto">
          <CheckCircle size={40} className="text-green-600" />
        </div>
        
        <div>
          <h2 className="text-2xl font-bold mb-2">Félicitations ! 🎉</h2>
          <p className="text-muted-foreground">
            Votre profil ChauffeurX est maintenant configuré et prêt à l'emploi
          </p>
        </div>

        <Card className="p-6 bg-gradient-to-r from-primary/10 to-secondary/10">
          <h3 className="font-semibold mb-4">Vous êtes maintenant prêt à :</h3>
          <div className="grid grid-cols-2 gap-3 text-sm">
            <div className="flex items-center space-x-2">
              <CheckCircle size={16} className="text-green-600" />
              <span>Voir les zones chaudes</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle size={16} className="text-green-600" />
              <span>Recevoir des recommandations IA</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle size={16} className="text-green-600" />
              <span>Suivre vos revenus</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle size={16} className="text-green-600" />
              <span>Optimiser vos trajets</span>
            </div>
          </div>
        </Card>

        <div className="space-y-3">
          <Button onClick={handleComplete} className="w-full" size="lg">
            Commencer à utiliser ChauffeurX
          </Button>
          <p className="text-xs text-muted-foreground">
            Vous pouvez modifier vos préférences à tout moment dans les paramètres
          </p>
        </div>
      </div>
    );
  }

  const step = tutorialSteps[currentStep];
  const IconComponent = step.icon;

  return (
    <div className="space-y-6">
      <div className="text-center">
        <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
          <IconComponent size={32} className="text-primary" />
        </div>
        <h2 className="text-2xl font-bold mb-2">Découvrez ChauffeurX</h2>
        <p className="text-muted-foreground">
          Apprenez à utiliser les fonctionnalités principales de l'application
        </p>
      </div>

      <div className="flex justify-center space-x-2 mb-6">
        {tutorialSteps.map((_, index) => (
          <div
            key={index}
            className={`w-2 h-2 rounded-full ${
              index === currentStep ? 'bg-primary' : 'bg-muted'
            }`}
          />
        ))}
      </div>

      <Card className="p-6">
        <div className="text-center mb-6">
          <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-3">
            <IconComponent size={24} className="text-primary" />
          </div>
          <h3 className="text-xl font-bold mb-2">{step.title}</h3>
          <p className="text-muted-foreground">{step.description}</p>
        </div>

        <div className="space-y-3">
          <h4 className="font-semibold text-sm">Fonctionnalités clés :</h4>
          <div className="grid grid-cols-1 gap-2">
            {step.features.map((feature, index) => (
              <div key={index} className="flex items-center space-x-2">
                <CheckCircle size={16} className="text-green-600" />
                <span className="text-sm">{feature}</span>
              </div>
            ))}
          </div>
        </div>
      </Card>

      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={handlePrevious}
          disabled={currentStep === 0}
        >
          <ChevronLeft className="w-4 h-4 mr-2" />
          Précédent
        </Button>
        
        <Badge variant="secondary">
          {currentStep + 1} / {tutorialSteps.length}
        </Badge>
        
        <Button onClick={handleNext}>
          {currentStep === tutorialSteps.length - 1 ? "Terminer" : "Suivant"}
          <ChevronRight className="w-4 h-4 ml-2" />
        </Button>
      </div>
    </div>
  );
}
