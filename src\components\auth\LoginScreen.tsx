import { useState } from "react";
import { signIn } from "@/lib/mockAuth";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Eye, EyeOff, Car, Mail, Lock, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface LoginScreenProps {
  onSwitchToSignup: () => void;
  onForgotPassword: () => void;
}

export default function LoginScreen({ onSwitchToSignup, onForgotPassword }: LoginScreenProps) {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const result = await signIn("credentials", {
        email,
        password,
        redirect: false,
      });

      if (result?.error) {
        toast({
          title: "Erreur de connexion",
          description: "Email ou mot de passe incorrect.",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Connexion réussie",
          description: "Bienvenue sur ChauffeurX !",
        });
        // Navigation will be handled by AuthWrapper
      }
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la connexion.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setIsLoading(true);
    try {
      await signIn("google", { callbackUrl: "/" });
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Impossible de se connecter avec Google.",
        variant: "destructive",
      });
      setIsLoading(false);
    }
  };

  // Demo login for existing driver
  const handleDemoLogin = async () => {
    setEmail("<EMAIL>");
    setPassword("chauffeur123");

    setTimeout(async () => {
      const result = await signIn("credentials", {
        email: "<EMAIL>",
        password: "chauffeur123",
        redirect: false,
      });

      if (!result?.error) {
        toast({
          title: "Connexion démo réussie",
          description: "Bienvenue dans l'application ChauffeurX !",
        });
      }
    }, 500);
  };

  // New user login to test onboarding
  const handleNewUserLogin = async () => {
    setEmail("<EMAIL>");
    setPassword("chauffeur123");

    setTimeout(async () => {
      const result = await signIn("credentials", {
        email: "<EMAIL>",
        password: "chauffeur123",
        redirect: false,
      });

      if (!result?.error) {
        toast({
          title: "Connexion réussie",
          description: "Configuration de votre profil chauffeur...",
        });
      }
    }, 500);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 flex items-center justify-center p-4 relative overflow-hidden">

      {/* Modern background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-32 h-32 bg-yellow-200/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-32 right-16 w-40 h-40 bg-blue-200/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-green-200/10 rounded-full blur-3xl"></div>
      </div>

      <Card className="w-full max-w-sm mx-4 shadow-2xl border-0 bg-white/80 backdrop-blur-xl rounded-3xl relative z-10">
        <CardHeader className="text-center pb-6">
          <div className="relative mx-auto mb-6">
            <div className="w-20 h-20 bg-gradient-to-br from-yellow-400 via-orange-500 to-red-500 rounded-3xl flex items-center justify-center shadow-2xl border-4 border-white/50">
              <Car size={36} className="text-white drop-shadow-lg" />
            </div>
            <div className="absolute -top-1 -right-1 w-6 h-6 bg-green-400 rounded-full animate-bounce shadow-lg"></div>
          </div>
          <CardTitle className="text-3xl font-black text-transparent bg-clip-text bg-gradient-to-r from-gray-800 to-gray-600 mb-2">
            ChauffeurX
          </CardTitle>
          <CardDescription className="text-gray-600 font-medium text-base">
            Connectez-vous à votre compte chauffeur VTC
          </CardDescription>
          <div className="w-12 h-1 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full mx-auto mt-3"></div>
        </CardHeader>
        
        <CardContent className="space-y-6 px-8 pb-8">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-3">
              <Label htmlFor="email" className="text-sm font-semibold text-gray-700">Email</Label>
              <div className="relative group">
                <Mail className="absolute left-4 top-4 h-5 w-5 text-gray-400 group-focus-within:text-yellow-500 transition-colors" />
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="pl-12 h-14 text-base rounded-2xl border-2 border-gray-200 focus:border-yellow-400 focus:ring-4 focus:ring-yellow-100 transition-all duration-300 bg-gray-50/50"
                  required
                />
              </div>
            </div>

            <div className="space-y-3">
              <Label htmlFor="password" className="text-sm font-semibold text-gray-700">Mot de passe</Label>
              <div className="relative group">
                <Lock className="absolute left-4 top-4 h-5 w-5 text-gray-400 group-focus-within:text-yellow-500 transition-colors" />
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  placeholder="••••••••"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="pl-12 pr-14 h-14 text-base rounded-2xl border-2 border-gray-200 focus:border-yellow-400 focus:ring-4 focus:ring-yellow-100 transition-all duration-300 bg-gray-50/50"
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-2 h-10 w-10 rounded-xl hover:bg-gray-100 transition-colors"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5 text-gray-400" />
                  ) : (
                    <Eye className="h-5 w-5 text-gray-400" />
                  )}
                </Button>
              </div>
            </div>
            
            <Button
              type="submit"
              className="w-full h-14 text-lg font-semibold rounded-2xl bg-gradient-to-r from-yellow-500 via-orange-500 to-red-500 hover:from-yellow-600 hover:via-orange-600 hover:to-red-600 shadow-xl border-0 transform hover:scale-105 transition-all duration-300 active:scale-95"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-3 h-5 w-5 animate-spin" />
                  Connexion en cours...
                </>
              ) : (
                <>
                  <span className="mr-2">🚀</span>
                  Se connecter
                </>
              )}
            </Button>
          </form>

          <div className="text-center">
            <Button
              variant="link"
              onClick={onForgotPassword}
              className="text-sm text-gray-500 hover:text-yellow-600 font-medium transition-colors"
            >
              Mot de passe oublié ?
            </Button>
          </div>

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t border-gray-200" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-white px-4 text-gray-500 font-medium">Ou continuer avec</span>
            </div>
          </div>

          <Button
            variant="outline"
            onClick={handleGoogleSignIn}
            className="w-full h-14 text-base font-semibold rounded-2xl border-2 border-gray-200 hover:border-gray-300 hover:bg-gray-50 transition-all duration-300 transform hover:scale-105 active:scale-95"
            disabled={isLoading}
          >
            <svg className="mr-3 h-5 w-5" viewBox="0 0 24 24">
              <path
                fill="#4285F4"
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
              />
              <path
                fill="#34A853"
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
              />
              <path
                fill="#FBBC05"
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
              />
              <path
                fill="#EA4335"
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
              />
            </svg>
            Continuer avec Google
          </Button>
          
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t border-gray-200" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-white px-4 text-gray-500 font-medium">Accès démo</span>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-3">
            <Button
              variant="secondary"
              onClick={handleDemoLogin}
              className="h-12 text-sm font-semibold rounded-xl bg-gradient-to-r from-blue-100 to-blue-200 hover:from-blue-200 hover:to-blue-300 text-blue-700 border-0 transform hover:scale-105 transition-all duration-300 active:scale-95"
            >
              <span className="mr-2">🚗</span>
              Chauffeur Existant
            </Button>
            <Button
              variant="outline"
              onClick={handleNewUserLogin}
              className="h-12 text-sm font-semibold rounded-xl border-2 border-green-200 hover:border-green-300 hover:bg-green-50 text-green-700 transform hover:scale-105 transition-all duration-300 active:scale-95"
            >
              <span className="mr-2">👤</span>
              Nouveau Chauffeur
            </Button>
          </div>

          <div className="text-center">
            <span className="text-gray-500 text-sm">Pas encore de compte ? </span>
            <Button
              variant="link"
              onClick={onSwitchToSignup}
              className="p-0 h-auto font-bold text-yellow-600 hover:text-yellow-700 transition-colors"
            >
              S'inscrire maintenant
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
