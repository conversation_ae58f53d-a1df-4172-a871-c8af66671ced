@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* ChauffeurX Design System - Light Mode */
    --background: 0 0% 98%;
    --foreground: 210 40% 8%;

    --card: 0 0% 100%;
    --card-foreground: 210 40% 8%;

    --popover: 0 0% 100%;
    --popover-foreground: 210 40% 8%;

    /* Vert citron principal */
    --primary: 90 60% 50%;
    --primary-foreground: 0 0% 100%;

    /* Bleu électrique */
    --secondary: 211 100% 50%;
    --secondary-foreground: 0 0% 100%;

    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;

    --accent: 90 60% 50%;
    --accent-foreground: 0 0% 100%;

    /* Rouge clair pour alertes */
    --destructive: 0 70% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 90 60% 50%;

    /* Zones chaudes et alertes */
    --hot-zone: 0 84% 60%;
    --warning: 45 100% 51%;
    --success: 120 60% 50%;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(90 60% 50%), hsl(120 60% 45%));
    --gradient-secondary: linear-gradient(135deg, hsl(211 100% 50%), hsl(220 100% 60%));
    
    /* Shadows */
    --shadow-card: 0 4px 6px -1px hsl(0 0% 0% / 0.1);
    --shadow-button: 0 2px 4px hsl(0 0% 0% / 0.1);
    --shadow-float: 0 10px 25px hsl(0 0% 0% / 0.15);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* ChauffeurX Design System - Dark Mode */
    --background: 0 0% 8%;
    --foreground: 0 0% 98%;

    --card: 0 0% 12%;
    --card-foreground: 0 0% 98%;

    --popover: 0 0% 12%;
    --popover-foreground: 0 0% 98%;

    --primary: 90 60% 50%;
    --primary-foreground: 0 0% 8%;

    --secondary: 211 100% 55%;
    --secondary-foreground: 0 0% 100%;

    --muted: 0 0% 15%;
    --muted-foreground: 0 0% 65%;

    --accent: 90 60% 50%;
    --accent-foreground: 0 0% 8%;

    --destructive: 0 70% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 0 0% 20%;
    --input: 0 0% 20%;
    --ring: 90 60% 50%;

    /* Zones chaudes et alertes - dark mode */
    --hot-zone: 0 84% 65%;
    --warning: 45 100% 55%;
    --success: 120 60% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}