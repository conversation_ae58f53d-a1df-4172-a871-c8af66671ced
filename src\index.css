@import 'leaflet/dist/leaflet.css';

/* Leaflet CSS - must be imported first */
@import 'leaflet/dist/leaflet.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* ChauffeurX Design System - Light Mode */
    --background: 0 0% 98%;
    --foreground: 210 40% 8%;

    --card: 0 0% 100%;
    --card-foreground: 210 40% 8%;

    --popover: 0 0% 100%;
    --popover-foreground: 210 40% 8%;

    /* Vert citron principal */
    --primary: 90 60% 50%;
    --primary-foreground: 0 0% 100%;

    /* Bleu électrique */
    --secondary: 211 100% 50%;
    --secondary-foreground: 0 0% 100%;

    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;

    --accent: 90 60% 50%;
    --accent-foreground: 0 0% 100%;

    /* Rouge clair pour alertes */
    --destructive: 0 70% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 90 60% 50%;

    /* Zones chaudes et alertes */
    --hot-zone: 0 84% 60%;
    --warning: 45 100% 51%;
    --success: 120 60% 50%;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(90 60% 50%), hsl(120 60% 45%));
    --gradient-secondary: linear-gradient(135deg, hsl(211 100% 50%), hsl(220 100% 60%));
    
    /* Shadows */
    --shadow-card: 0 4px 6px -1px hsl(0 0% 0% / 0.1);
    --shadow-button: 0 2px 4px hsl(0 0% 0% / 0.1);
    --shadow-float: 0 10px 25px hsl(0 0% 0% / 0.15);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* ChauffeurX Design System - Dark Mode */
    --background: 0 0% 8%;
    --foreground: 0 0% 98%;

    --card: 0 0% 12%;
    --card-foreground: 0 0% 98%;

    --popover: 0 0% 12%;
    --popover-foreground: 0 0% 98%;

    --primary: 90 60% 50%;
    --primary-foreground: 0 0% 8%;

    --secondary: 211 100% 55%;
    --secondary-foreground: 0 0% 100%;

    --muted: 0 0% 15%;
    --muted-foreground: 0 0% 65%;

    --accent: 90 60% 50%;
    --accent-foreground: 0 0% 8%;

    --destructive: 0 70% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 0 0% 20%;
    --input: 0 0% 20%;
    --ring: 90 60% 50%;

    /* Zones chaudes et alertes - dark mode */
    --hot-zone: 0 84% 65%;
    --warning: 45 100% 55%;
    --success: 120 60% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Uber-Style Design System Variables */
:root {
  /* Primary Colors - Taxi/VTC Theme */
  --uber-black: #000000;
  --uber-white: #ffffff;
  --uber-yellow: #ffb000;
  --uber-orange: #ff6b35;
  --uber-blue: #1fbad3;
  --uber-green: #04a96d;
  --uber-red: #e74c3c;

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, #ffb000 0%, #ff6b35 100%);
  --gradient-secondary: linear-gradient(135deg, #1fbad3 0%, #04a96d 100%);
  --gradient-surface: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-float: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* Border Radius */
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 24px;
  --radius-full: 9999px;

  /* Spacing */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 16px;
  --space-lg: 24px;
  --space-xl: 32px;
  --space-2xl: 48px;
}

/* Mobile-first optimizations */
@media (max-width: 768px) {
  /* Ensure touch targets are at least 44px */
  button, .button, [role="button"] {
    min-height: 44px;
    min-width: 44px;
  }

  /* Improve touch scrolling */
  * {
    -webkit-overflow-scrolling: touch;
  }

  /* Prevent zoom on input focus */
  input, select, textarea {
    font-size: 16px;
  }

  /* Optimize tap highlights */
  * {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  }

  /* Safe area handling for notched devices */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
}

/* Custom animations for taxi and floating elements */
@keyframes float {
  0% { transform: translateX(-100px) translateY(0px); }
  25% { transform: translateX(25vw) translateY(-10px); }
  50% { transform: translateX(50vw) translateY(5px); }
  75% { transform: translateX(75vw) translateY(-5px); }
  100% { transform: translateX(calc(100vw + 100px)) translateY(0px); }
}

@keyframes roadLines {
  from { transform: translateX(0); }
  to { transform: translateX(50px); }
}

/* Smooth transitions for modern UI */
.transition-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Uber-Style Component Classes */

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Uber-style buttons */
.btn-uber-primary {
  background: var(--gradient-primary);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  padding: 12px 24px;
  font-weight: 600;
  font-size: 16px;
  min-height: 48px;
  box-shadow: var(--shadow-md);
  transition: all 0.2s ease;
  transform: translateY(0);
}

.btn-uber-primary:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}

.btn-uber-primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* Uber-style cards */
.card-uber {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.card-uber-elevated {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  border: none;
  overflow: hidden;
}

/* Status indicators */
.status-online {
  background: var(--uber-green);
  color: white;
  border-radius: var(--radius-full);
  padding: 4px 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-offline {
  background: var(--uber-red);
  color: white;
  border-radius: var(--radius-full);
  padding: 4px 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-busy {
  background: var(--uber-orange);
  color: white;
  border-radius: var(--radius-full);
  padding: 4px 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Modern input styles */
.input-uber {
  background: #f8f9fa;
  border: 2px solid transparent;
  border-radius: var(--radius-lg);
  padding: 16px 20px;
  font-size: 16px;
  transition: all 0.2s ease;
  min-height: 56px;
}

.input-uber:focus {
  outline: none;
  border-color: var(--uber-yellow);
  background: white;
  box-shadow: 0 0 0 4px rgba(255, 176, 0, 0.1);
}

/* Floating action button */
.fab-uber {
  position: fixed;
  bottom: 80px;
  right: 20px;
  width: 56px;
  height: 56px;
  background: var(--gradient-primary);
  border-radius: 50%;
  border: none;
  box-shadow: var(--shadow-float);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  transition: all 0.3s ease;
  z-index: 1000;
}

.fab-uber:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-xl);
}

/* Navigation styles */
.nav-uber {
  background: white;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.nav-item-uber {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 12px;
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
  min-height: 44px;
  min-width: 44px;
}

.nav-item-uber.active {
  background: rgba(255, 176, 0, 0.1);
  color: var(--uber-yellow);
}

.nav-item-uber:hover {
  background: rgba(0, 0, 0, 0.05);
}

/* Typography */
.text-uber-title {
  font-size: 28px;
  font-weight: 700;
  line-height: 1.2;
  color: var(--uber-black);
}

.text-uber-subtitle {
  font-size: 18px;
  font-weight: 600;
  line-height: 1.3;
  color: var(--uber-black);
}

.text-uber-body {
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5;
  color: #374151;
}

.text-uber-caption {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
  color: #6b7280;
}