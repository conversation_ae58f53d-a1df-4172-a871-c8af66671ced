import { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Circle } from 'react-leaflet';
import { HotZone, Coordinates } from '@/services/mapService';
import { Button } from './ui/button';
import { Navigation, Flame, MapPin, Zap } from 'lucide-react';
import { Card } from './ui/card';
import L from 'leaflet';

// Fix for default markers in react-leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Custom icons for different zone intensities
const createCustomIcon = (intensity: string) => {
  const color = intensity === 'Très chaud' ? '#ef4444' :
                intensity === 'Chaud' ? '#f97316' : '#10b981';

  return L.divIcon({
    html: `<div style="background-color: ${color}; width: 20px; height: 20px; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>`,
    className: 'custom-div-icon',
    iconSize: [20, 20],
    iconAnchor: [10, 10]
  });
};

interface SimpleMapProps {
  hotZones: HotZone[];
  currentLocation: Coordinates | null;
  onZoneClick: (zone: HotZone) => void;
  onNavigateToZone: (zone: HotZone) => void;
  className?: string;
}

export default function SimpleMap({
  hotZones,
  currentLocation,
  onZoneClick,
  onNavigateToZone,
  className = ""
}: SimpleMapProps) {
  const [selectedZone, setSelectedZone] = useState<HotZone | null>(null);

  // Coordonnées réelles d'Abidjan
  const abidjanCenter: [number, number] = [5.3364, -4.0267];

  // Coordonnées réelles des zones d'Abidjan
  const getZoneCoordinates = (zone: HotZone): [number, number] => {
    const zoneCoords: { [key: string]: [number, number] } = {
      'Plateau': [5.3197, -4.0267],
      'Adjamé': [5.3553, -4.0267],
      'Cocody': [5.3364, -3.9867],
      'Zone 4': [5.3097, -4.0467],
      'Marcory': [5.2897, -4.0167],
      'Treichville': [5.2997, -4.0367],
      'Yopougon': [5.3664, -4.0867],
      'Riviera': [5.3764, -3.9567],
      'Abobo': [5.4164, -4.0167],
      'Port-Bouët': [5.2497, -3.9767]
    };

    return zoneCoords[zone.name] || [5.3364 + (Math.random() - 0.5) * 0.1, -4.0267 + (Math.random() - 0.5) * 0.1];
  };

  const handleZoneClick = (zone: HotZone) => {
    setSelectedZone(zone);
    onZoneClick(zone);
  };

  const getIntensityColor = (intensity: string) => {
    switch (intensity) {
      case 'Très chaud': return '#ef4444';
      case 'Chaud': return '#f97316';
      case 'Modéré': return '#10b981';
      default: return '#6b7280';
    }
  };

  const getCircleRadius = (demandLevel: number) => {
    return Math.max(200, demandLevel * 10); // Rayon basé sur le niveau de demande
  };

  return (
    <div className={`relative ${className}`} style={{ height: '100%', width: '100%' }}>
      <MapContainer
        center={abidjanCenter}
        zoom={12}
        style={{ height: '100%', width: '100%' }}
        className="z-0"
      >
        {/* Couche OpenStreetMap */}
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />

        {/* Position actuelle du chauffeur */}
        {currentLocation && (
          <Marker position={[currentLocation.lat, currentLocation.lng]}>
            <Popup>
              <div className="text-center">
                <MapPin className="w-4 h-4 text-blue-500 mx-auto mb-1" />
                <span className="text-sm font-medium">Votre position</span>
              </div>
            </Popup>
          </Marker>
        )}

        {/* Zones chaudes avec cercles et marqueurs */}
        {hotZones.map((zone) => {
          const coords = getZoneCoordinates(zone);
          const color = getIntensityColor(zone.intensity);

          return (
            <div key={zone.id}>
              {/* Cercle de zone chaude */}
              <Circle
                center={coords}
                radius={getCircleRadius(zone.demandLevel)}
                pathOptions={{
                  color: color,
                  fillColor: color,
                  fillOpacity: 0.2,
                  weight: 2
                }}
              />

              {/* Marqueur de zone */}
              <Marker
                position={coords}
                icon={createCustomIcon(zone.intensity)}
                eventHandlers={{
                  click: () => handleZoneClick(zone)
                }}
              >
                <Popup>
                  <div className="min-w-[200px]">
                    <div className="flex items-center space-x-2 mb-2">
                      <Flame className="w-4 h-4" style={{ color }} />
                      <h3 className="font-semibold">{zone.name}</h3>
                      <span
                        className="text-xs px-2 py-1 rounded text-white"
                        style={{ backgroundColor: color }}
                      >
                        {zone.intensity}
                      </span>
                    </div>

                    <div className="space-y-1 text-sm mb-3">
                      <div className="flex justify-between">
                        <span>Demande:</span>
                        <span className="font-medium">{zone.demandLevel}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Attente:</span>
                        <span className="font-medium">{zone.estimatedWaitTime} min</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Revenu moyen:</span>
                        <span className="font-medium">{zone.averageRideValue.toLocaleString()} CFA</span>
                      </div>
                    </div>

                    <Button
                      size="sm"
                      onClick={() => onNavigateToZone(zone)}
                      className="w-full"
                    >
                      <Navigation className="w-3 h-3 mr-1" />
                      Naviguer vers cette zone
                    </Button>
                  </div>
                </Popup>
              </Marker>
            </div>
          );
        })}
      </MapContainer>

      {/* Légende de la carte */}
      <div className="absolute bottom-4 left-4 bg-white/95 backdrop-blur-sm rounded-lg p-3 shadow-lg z-10">
        <h4 className="font-semibold text-sm mb-2">Zones de demande</h4>
        <div className="space-y-1 text-xs">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
            <span>Très chaud (80%+)</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
            <span>Chaud (65-79%)</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <span>Modéré (&lt;65%)</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
            <span>Votre position</span>
          </div>
        </div>
      </div>

      {/* Informations de la carte */}
      <div className="absolute top-4 left-4 bg-white/95 backdrop-blur-sm rounded-lg p-3 shadow-lg z-10">
        <div className="flex items-center space-x-2 mb-1">
          <MapPin size={16} className="text-blue-500" />
          <span className="text-sm font-medium">Abidjan, Côte d'Ivoire</span>
        </div>
        <div className="flex items-center space-x-2">
          <Zap size={14} className="text-orange-500" />
          <span className="text-xs text-gray-600">
            {hotZones.filter(z => z.demandLevel > 70).length} zones chaudes actives
          </span>
        </div>
      </div>
    </div>
  );
}
