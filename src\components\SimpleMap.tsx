import { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Circle, useMap } from 'react-leaflet';
import { HotZone, Coordinates } from '@/services/mapService';
import { Button } from './ui/button';
import { Navigation, Flame, MapPin, Zap, Locate, RotateCcw } from 'lucide-react';
import { Card } from './ui/card';
import L from 'leaflet';

// Fix for default markers in react-leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Enhanced custom icons for different zone intensities
const createCustomIcon = (intensity: string, demandLevel: number) => {
  const color = intensity === 'Très chaud' ? '#ef4444' :
                intensity === 'Chaud' ? '#f97316' : '#10b981';

  const size = Math.max(24, Math.min(40, 20 + (demandLevel / 100) * 20));

  return L.divIcon({
    html: `
      <div style="
        background: ${color};
        width: ${size}px;
        height: ${size}px;
        border-radius: 50%;
        border: 3px solid white;
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        color: white;
        font-size: ${Math.max(10, size * 0.3)}px;
      ">
        ${demandLevel}%
      </div>
    `,
    className: 'custom-zone-icon',
    iconSize: [size, size],
    iconAnchor: [size/2, size/2]
  });
};

// Driver location icon
const createDriverIcon = () => {
  return L.divIcon({
    html: `
      <div style="
        background: #3b82f6;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 4px solid white;
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        position: relative;
      ">
        <div style="
          position: absolute;
          top: -8px;
          left: -8px;
          width: 36px;
          height: 36px;
          border: 2px solid #3b82f6;
          border-radius: 50%;
          opacity: 0.3;
          animation: pulse 2s infinite;
        "></div>
      </div>
      <style>
        @keyframes pulse {
          0% { transform: scale(0.8); opacity: 0.3; }
          50% { transform: scale(1.2); opacity: 0.1; }
          100% { transform: scale(0.8); opacity: 0.3; }
        }
      </style>
    `,
    className: 'driver-location-icon',
    iconSize: [20, 20],
    iconAnchor: [10, 10]
  });
};

// Map controls component
function MapControls({ onLocate, onReset }: { onLocate: () => void; onReset: () => void }) {
  return (
    <div className="absolute top-4 right-4 z-10 flex flex-col space-y-2">
      <Button
        onClick={onLocate}
        className="w-12 h-12 bg-white hover:bg-gray-50 text-gray-700 rounded-full shadow-lg border border-gray-200"
        size="sm"
      >
        <Locate size={20} />
      </Button>
      <Button
        onClick={onReset}
        className="w-12 h-12 bg-white hover:bg-gray-50 text-gray-700 rounded-full shadow-lg border border-gray-200"
        size="sm"
      >
        <RotateCcw size={20} />
      </Button>
    </div>
  );
}

interface SimpleMapProps {
  hotZones: HotZone[];
  currentLocation: Coordinates | null;
  onZoneClick: (zone: HotZone) => void;
  onNavigateToZone: (zone: HotZone) => void;
  className?: string;
}

export default function SimpleMap({
  hotZones,
  currentLocation,
  onZoneClick,
  onNavigateToZone,
  className = ""
}: SimpleMapProps) {
  const [selectedZone, setSelectedZone] = useState<HotZone | null>(null);
  const [mapInstance, setMapInstance] = useState<L.Map | null>(null);

  // Coordonnées réelles d'Abidjan
  const abidjanCenter: [number, number] = [5.3364, -4.0267];

  // Coordonnées réelles des zones d'Abidjan avec plus de précision
  const getZoneCoordinates = (zone: HotZone): [number, number] => {
    const zoneCoords: { [key: string]: [number, number] } = {
      'Plateau': [5.3197, -4.0267],
      'Adjamé': [5.3553, -4.0267],
      'Cocody': [5.3364, -3.9867],
      'Zone 4': [5.3097, -4.0467],
      'Marcory': [5.2897, -4.0167],
      'Treichville': [5.2997, -4.0367],
      'Yopougon': [5.3664, -4.0867],
      'Riviera': [5.3764, -3.9567],
      'Abobo': [5.4164, -4.0167],
      'Port-Bouët': [5.2497, -3.9767],
      'Koumassi': [5.2764, -3.9567],
      'Bingerville': [5.3564, -3.8967],
      'Anyama': [5.4964, -4.0567]
    };

    return zoneCoords[zone.name] || [5.3364 + (Math.random() - 0.5) * 0.1, -4.0267 + (Math.random() - 0.5) * 0.1];
  };

  const handleZoneClick = (zone: HotZone) => {
    setSelectedZone(zone);
    onZoneClick(zone);
  };

  const handleLocateUser = () => {
    if (mapInstance && currentLocation) {
      mapInstance.setView([currentLocation.lat, currentLocation.lng], 15);
    }
  };

  const handleResetView = () => {
    if (mapInstance) {
      mapInstance.setView(abidjanCenter, 12);
    }
  };

  const getIntensityColor = (intensity: string) => {
    switch (intensity) {
      case 'Très chaud': return '#ef4444';
      case 'Chaud': return '#f97316';
      case 'Modéré': return '#10b981';
      default: return '#6b7280';
    }
  };

  const getCircleRadius = (demandLevel: number) => {
    return Math.max(200, demandLevel * 10); // Rayon basé sur le niveau de demande
  };

  return (
    <div className={`relative ${className}`} style={{ height: '100%', width: '100%' }}>
      <MapContainer
        center={abidjanCenter}
        zoom={12}
        style={{ height: '100%', width: '100%' }}
        className="z-0"
        whenCreated={setMapInstance}
        zoomControl={false}
      >
        {/* Couche OpenStreetMap optimisée */}
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          maxZoom={19}
          minZoom={10}
          updateWhenIdle={true}
          keepBuffer={2}
        />

        {/* Position actuelle du chauffeur avec animation */}
        {currentLocation && (
          <Marker
            position={[currentLocation.lat, currentLocation.lng]}
            icon={createDriverIcon()}
          >
            <Popup>
              <div className="text-center p-2">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                  <MapPin className="w-4 h-4 text-blue-600" />
                </div>
                <span className="text-sm font-semibold text-gray-900">Votre position</span>
                <p className="text-xs text-gray-600 mt-1">Position GPS active</p>
              </div>
            </Popup>
          </Marker>
        )}

        {/* Zones chaudes avec cercles et marqueurs améliorés */}
        {hotZones.map((zone) => {
          const coords = getZoneCoordinates(zone);
          const color = getIntensityColor(zone.intensity);

          return (
            <div key={zone.id}>
              {/* Cercle de zone chaude avec animation */}
              <Circle
                center={coords}
                radius={getCircleRadius(zone.demandLevel)}
                pathOptions={{
                  color: color,
                  fillColor: color,
                  fillOpacity: zone.intensity === 'Très chaud' ? 0.3 : 0.2,
                  weight: zone.intensity === 'Très chaud' ? 3 : 2,
                  dashArray: zone.intensity === 'Très chaud' ? '5, 5' : undefined
                }}
              />

              {/* Marqueur de zone amélioré */}
              <Marker
                position={coords}
                icon={createCustomIcon(zone.intensity, zone.demandLevel)}
                eventHandlers={{
                  click: () => handleZoneClick(zone)
                }}
              >
                <Popup maxWidth={250} className="custom-popup">
                  <div className="p-3">
                    <div className="flex items-center space-x-3 mb-3">
                      <div
                        className="w-8 h-8 rounded-full flex items-center justify-center"
                        style={{ backgroundColor: color }}
                      >
                        <Flame className="w-4 h-4 text-white" />
                      </div>
                      <div>
                        <h3 className="font-bold text-gray-900">{zone.name}</h3>
                        <span
                          className="text-xs px-2 py-1 rounded-full text-white font-medium"
                          style={{ backgroundColor: color }}
                        >
                          {zone.intensity}
                        </span>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-3 text-sm mb-4">
                      <div className="text-center p-2 bg-gray-50 rounded-lg">
                        <div className="font-bold text-lg text-gray-900">{zone.demandLevel}%</div>
                        <div className="text-gray-600">Demande</div>
                      </div>
                      <div className="text-center p-2 bg-gray-50 rounded-lg">
                        <div className="font-bold text-lg text-gray-900">{zone.estimatedWaitTime}</div>
                        <div className="text-gray-600">min d'attente</div>
                      </div>
                    </div>

                    <div className="text-center mb-3">
                      <div className="text-lg font-bold text-green-600">
                        {zone.averageRideValue.toLocaleString()} CFA
                      </div>
                      <div className="text-sm text-gray-600">Revenu moyen par course</div>
                    </div>

                    <Button
                      size="sm"
                      onClick={() => onNavigateToZone(zone)}
                      className="w-full bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium"
                    >
                      <Navigation className="w-4 h-4 mr-2" />
                      Naviguer vers cette zone
                    </Button>
                  </div>
                </Popup>
              </Marker>
            </div>
          );
        })}
      </MapContainer>

      {/* Map Controls */}
      <MapControls onLocate={handleLocateUser} onReset={handleResetView} />

      {/* Légende de la carte */}
      <div className="absolute bottom-4 left-4 bg-white/95 backdrop-blur-sm rounded-lg p-3 shadow-lg z-10">
        <h4 className="font-semibold text-sm mb-2">Zones de demande</h4>
        <div className="space-y-1 text-xs">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
            <span>Très chaud (80%+)</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
            <span>Chaud (65-79%)</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <span>Modéré (&lt;65%)</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
            <span>Votre position</span>
          </div>
        </div>
      </div>

      {/* Informations de la carte */}
      <div className="absolute top-4 left-4 bg-white/95 backdrop-blur-sm rounded-lg p-3 shadow-lg z-10">
        <div className="flex items-center space-x-2 mb-1">
          <MapPin size={16} className="text-blue-500" />
          <span className="text-sm font-medium">Abidjan, Côte d'Ivoire</span>
        </div>
        <div className="flex items-center space-x-2">
          <Zap size={14} className="text-orange-500" />
          <span className="text-xs text-gray-600">
            {hotZones.filter(z => z.demandLevel > 70).length} zones chaudes actives
          </span>
        </div>
      </div>
    </div>
  );
}
