interface WeatherData {
  temperature: number;
  condition: string;
  icon: string;
  humidity: number;
  windSpeed: number;
  description: string;
}

interface WeatherResponse {
  current: {
    temp_c: number;
    condition: {
      text: string;
      icon: string;
    };
    humidity: number;
    wind_kph: number;
  };
}

class WeatherService {
  private apiKey: string;
  private baseUrl = 'https://api.weatherapi.com/v1';
  private abidjanCoords = { lat: 5.3600, lon: -4.0083 };

  constructor() {
    this.apiKey = import.meta.env.VITE_WEATHER_API_KEY;
    if (!this.apiKey) {
      throw new Error('Weather API key not found in environment variables');
    }
  }

  async getCurrentWeather(): Promise<WeatherData> {
    try {
      const response = await fetch(
        `${this.baseUrl}/current.json?key=${this.apiKey}&q=${this.abidjanCoords.lat},${this.abidjanCoords.lon}&aqi=no`
      );

      if (!response.ok) {
        throw new Error(`Weather API error: ${response.status} ${response.statusText}`);
      }

      const data: WeatherResponse = await response.json();

      return {
        temperature: Math.round(data.current.temp_c),
        condition: data.current.condition.text,
        icon: data.current.condition.icon,
        humidity: data.current.humidity,
        windSpeed: Math.round(data.current.wind_kph),
        description: this.getWeatherDescription(data.current.condition.text)
      };
    } catch (error) {
      console.error('Error fetching weather data:', error);
      // Return fallback weather data
      return this.getFallbackWeather();
    }
  }

  async getWeatherForecast(): Promise<WeatherData[]> {
    try {
      const response = await fetch(
        `${this.baseUrl}/forecast.json?key=${this.apiKey}&q=${this.abidjanCoords.lat},${this.abidjanCoords.lon}&days=3&aqi=no&alerts=no`
      );

      if (!response.ok) {
        throw new Error(`Weather API error: ${response.status}`);
      }

      const data = await response.json();
      
      return data.forecast.forecastday.map((day: any) => ({
        temperature: Math.round(day.day.avgtemp_c),
        condition: day.day.condition.text,
        icon: day.day.condition.icon,
        humidity: day.day.avghumidity,
        windSpeed: Math.round(day.day.maxwind_kph),
        description: this.getWeatherDescription(day.day.condition.text)
      }));
    } catch (error) {
      console.error('Error fetching weather forecast:', error);
      return [this.getFallbackWeather()];
    }
  }

  getWeatherImpactOnDemand(weather: WeatherData): string {
    const condition = weather.condition.toLowerCase();
    
    if (condition.includes('rain') || condition.includes('pluie')) {
      return "Pluie prévue - demandes en hausse (+20%)";
    } else if (condition.includes('sunny') || condition.includes('clear') || condition.includes('ensoleillé')) {
      return "Temps ensoleillé - demandes normales";
    } else if (condition.includes('cloud') || condition.includes('nuageux')) {
      return "Temps nuageux - demandes stables";
    } else if (condition.includes('storm') || condition.includes('orage')) {
      return "Orage prévu - forte demande attendue (+35%)";
    } else if (weather.temperature > 32) {
      return "Forte chaleur - demandes climatisées en hausse";
    } else {
      return "Conditions météo favorables";
    }
  }

  private getWeatherDescription(condition: string): string {
    const descriptions: { [key: string]: string } = {
      'sunny': 'Ensoleillé',
      'clear': 'Dégagé',
      'partly cloudy': 'Partiellement nuageux',
      'cloudy': 'Nuageux',
      'overcast': 'Couvert',
      'mist': 'Brumeux',
      'patchy rain possible': 'Pluie possible',
      'patchy snow possible': 'Neige possible',
      'patchy sleet possible': 'Grésil possible',
      'patchy freezing drizzle possible': 'Bruine verglaçante possible',
      'thundery outbreaks possible': 'Orages possibles',
      'blowing snow': 'Poudrerie',
      'blizzard': 'Blizzard',
      'fog': 'Brouillard',
      'freezing fog': 'Brouillard givrant',
      'patchy light drizzle': 'Bruine légère par endroits',
      'light drizzle': 'Bruine légère',
      'freezing drizzle': 'Bruine verglaçante',
      'heavy freezing drizzle': 'Forte bruine verglaçante',
      'patchy light rain': 'Pluie légère par endroits',
      'light rain': 'Pluie légère',
      'moderate rain at times': 'Pluie modérée par moments',
      'moderate rain': 'Pluie modérée',
      'heavy rain at times': 'Forte pluie par moments',
      'heavy rain': 'Forte pluie',
      'light freezing rain': 'Pluie verglaçante légère',
      'moderate or heavy freezing rain': 'Pluie verglaçante modérée à forte',
      'light sleet': 'Grésil léger',
      'moderate or heavy sleet': 'Grésil modéré à fort',
      'patchy light snow': 'Neige légère par endroits',
      'light snow': 'Neige légère',
      'patchy moderate snow': 'Neige modérée par endroits',
      'moderate snow': 'Neige modérée',
      'patchy heavy snow': 'Forte neige par endroits',
      'heavy snow': 'Forte neige',
      'ice pellets': 'Granules de glace',
      'light rain shower': 'Averse légère',
      'moderate or heavy rain shower': 'Averse modérée à forte',
      'torrential rain shower': 'Averse torrentielle',
      'light sleet showers': 'Averses de grésil léger',
      'moderate or heavy sleet showers': 'Averses de grésil modéré à fort',
      'light snow showers': 'Averses de neige légère',
      'moderate or heavy snow showers': 'Averses de neige modérée à forte',
      'light showers of ice pellets': 'Averses légères de granules de glace',
      'moderate or heavy showers of ice pellets': 'Averses modérées à fortes de granules de glace',
      'patchy light rain with thunder': 'Pluie légère avec tonnerre par endroits',
      'moderate or heavy rain with thunder': 'Pluie modérée à forte avec tonnerre',
      'patchy light snow with thunder': 'Neige légère avec tonnerre par endroits',
      'moderate or heavy snow with thunder': 'Neige modérée à forte avec tonnerre'
    };

    return descriptions[condition.toLowerCase()] || condition;
  }

  private getFallbackWeather(): WeatherData {
    return {
      temperature: 28,
      condition: 'Partly Cloudy',
      icon: '//cdn.weatherapi.com/weather/64x64/day/116.png',
      humidity: 75,
      windSpeed: 12,
      description: 'Partiellement nuageux'
    };
  }
}

export const weatherService = new WeatherService();
export type { WeatherData };
