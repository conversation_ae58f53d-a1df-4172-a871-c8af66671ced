import { useState } from 'react';
import { Button } from './ui/button';
import { Card } from './ui/card';
import { HotZone, Coordinates } from '@/services/mapService';
import { Navigation, Flame, MapPin, Zap } from 'lucide-react';

interface FallbackMapProps {
  hotZones: HotZone[];
  currentLocation: Coordinates | null;
  onZoneClick: (zone: HotZone) => void;
  onNavigateToZone: (zone: HotZone) => void;
  className?: string;
}

export default function FallbackMap({ 
  hotZones, 
  currentLocation, 
  onZoneClick, 
  onNavigateToZone,
  className = ""
}: FallbackMapProps) {
  const [selectedZone, setSelectedZone] = useState<HotZone | null>(null);

  const getZonePosition = (zone: HotZone, index: number) => {
    // Convert real coordinates to screen positions (simplified)
    const positions = [
      { top: "30%", left: "45%" }, // Plateau
      { top: "25%", left: "30%" }, // Adjamé
      { top: "40%", left: "60%" }, // Cocody
      { top: "55%", left: "40%" }, // Zone 4
      { top: "45%", left: "35%" }, // Treichville
      { top: "20%", left: "25%" }, // Yopougon
      { top: "15%", left: "65%" }, // Riviera
    ];
    return positions[index] || { top: "50%", left: "50%" };
  };

  const handleZoneClick = (zone: HotZone) => {
    setSelectedZone(zone);
    onZoneClick(zone);
  };

  return (
    <div className={`relative ${className}`}>
      {/* Simulated Map Background */}
      <div className="w-full h-full bg-gradient-to-br from-muted/20 to-muted/40 relative overflow-hidden">
        
        {/* Driver's current location */}
        {currentLocation && (
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <div className="w-4 h-4 bg-blue-500 rounded-full border-2 border-white shadow-lg animate-pulse"></div>
            <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2">
              <span className="text-xs font-medium bg-card px-2 py-1 rounded shadow-lg whitespace-nowrap">
                Vous êtes ici
              </span>
            </div>
          </div>
        )}

        {/* Hot zones */}
        {hotZones.map((zone, index) => {
          const position = getZonePosition(zone, index);
          return (
            <div
              key={zone.id}
              className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer"
              style={{ top: position.top, left: position.left }}
              onClick={() => handleZoneClick(zone)}
            >
              <div className={`
                w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110
                ${zone.intensity === "Très chaud" ? "bg-red-500/20 border-2 border-red-500 animate-pulse" : ""}
                ${zone.intensity === "Chaud" ? "bg-orange-500/20 border-2 border-orange-500" : ""}
                ${zone.intensity === "Modéré" ? "bg-green-500/20 border-2 border-green-500" : ""}
              `}>
                <Flame 
                  size={24} 
                  className={`
                    ${zone.intensity === "Très chaud" ? "text-red-500" : ""}
                    ${zone.intensity === "Chaud" ? "text-orange-500" : ""}
                    ${zone.intensity === "Modéré" ? "text-green-500" : ""}
                  `}
                />
              </div>
              <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2">
                <div className="text-center">
                  <span className="text-xs font-medium bg-card px-2 py-1 rounded shadow-lg whitespace-nowrap block">
                    {zone.name}
                  </span>
                  <span className="text-xs text-muted-foreground bg-card/80 px-1 rounded mt-1 block">
                    {zone.demandLevel}%
                  </span>
                </div>
              </div>
            </div>
          );
        })}

        {/* Simulated street lines */}
        <div className="absolute inset-0 opacity-30">
          <svg className="w-full h-full">
            <path
              d="M 20 100 Q 200 50 400 150"
              stroke="currentColor"
              strokeWidth="2"
              fill="none"
              className="text-muted-foreground"
            />
            <path
              d="M 50 200 Q 300 180 380 300"
              stroke="currentColor"
              strokeWidth="2"
              fill="none"
              className="text-muted-foreground"
            />
            <path
              d="M 100 50 L 350 80 L 380 250"
              stroke="currentColor"
              strokeWidth="1"
              fill="none"
              className="text-muted-foreground"
            />
          </svg>
        </div>

        {/* Zone details popup */}
        {selectedZone && (
          <Card className="absolute top-4 right-4 p-4 bg-card/95 backdrop-blur-sm max-w-xs">
            <div className="flex items-center space-x-2 mb-3">
              <Flame className="w-4 h-4 text-orange-500" />
              <h3 className="font-semibold">{selectedZone.name}</h3>
            </div>
            
            <div className="space-y-2 text-sm mb-3">
              <div className="flex justify-between">
                <span>Demande:</span>
                <span className="font-medium">{selectedZone.demandLevel}%</span>
              </div>
              <div className="flex justify-between">
                <span>Attente:</span>
                <span className="font-medium">{selectedZone.estimatedWaitTime} min</span>
              </div>
              <div className="flex justify-between">
                <span>Revenu moyen:</span>
                <span className="font-medium">{selectedZone.averageRideValue.toLocaleString()} CFA</span>
              </div>
            </div>
            
            <div className="flex space-x-2">
              <Button
                size="sm"
                onClick={() => onNavigateToZone(selectedZone)}
                className="flex-1"
              >
                <Navigation className="w-3 h-3 mr-1" />
                Naviguer
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setSelectedZone(null)}
              >
                Fermer
              </Button>
            </div>
          </Card>
        )}

        {/* Map legend */}
        <div className="absolute bottom-4 left-4 bg-card/95 backdrop-blur-sm rounded-lg p-3 shadow-lg">
          <h4 className="font-semibold text-sm mb-2">Légende</h4>
          <div className="space-y-1 text-xs">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <span>Très chaud (80%+)</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
              <span>Chaud (65-79%)</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span>Modéré (&lt;65%)</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span>Votre position</span>
            </div>
          </div>
        </div>

        {/* Info overlay */}
        <div className="absolute top-4 left-4 bg-card/95 backdrop-blur-sm rounded-lg p-3 shadow-lg">
          <div className="flex items-center space-x-2">
            <MapPin size={16} className="text-primary" />
            <span className="text-sm font-medium">
              {currentLocation ? "Position GPS active" : "Position simulée"}
            </span>
          </div>
          <div className="flex items-center space-x-2 mt-1">
            <Zap size={14} className="text-orange-500" />
            <span className="text-xs text-muted-foreground">
              {hotZones.filter(z => z.demandLevel > 70).length} zones chaudes actives
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
