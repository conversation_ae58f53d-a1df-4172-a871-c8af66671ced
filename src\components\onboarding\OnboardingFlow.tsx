import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { ChevronLeft, ChevronRight, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { driverService, type OnboardingData } from "@/services/driverService";
import VehicleInfoStep from "./VehicleInfoStep";
import LicenseVerificationStep from "./LicenseVerificationStep";
import ProfilePhotoStep from "./ProfilePhotoStep";
import PreferencesStep from "./PreferencesStep";
import TutorialStep from "./TutorialStep";

interface OnboardingFlowProps {
  userData: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
  };
  userId: string;
  onComplete: (driverProfile: any) => void;
}

interface OnboardingData {
  // Vehicle Information
  vehicleMake: string;
  vehicleModel: string;
  vehicleYear: number;
  vehicleColor: string;
  licensePlate: string;
  
  // License Information
  licenseNumber: string;
  licenseExpiry: string;
  licensePhoto?: File;
  
  // Profile
  profilePhoto?: File;
  
  // Preferences
  preferredZones: string[];
  workingHours: {
    start: string;
    end: string;
  };
  notifications: {
    hotZones: boolean;
    weather: boolean;
    earnings: boolean;
  };
}

const steps = [
  {
    id: 1,
    title: "Informations du véhicule",
    description: "Renseignez les détails de votre véhicule"
  },
  {
    id: 2,
    title: "Vérification du permis",
    description: "Téléchargez votre permis de conduire"
  },
  {
    id: 3,
    title: "Photo de profil",
    description: "Ajoutez votre photo de profil"
  },
  {
    id: 4,
    title: "Préférences",
    description: "Configurez vos préférences de travail"
  },
  {
    id: 5,
    title: "Tutoriel",
    description: "Découvrez les fonctionnalités de l'app"
  }
];

export default function OnboardingFlow({ userData, userId, onComplete }: OnboardingFlowProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const [isCompleting, setIsCompleting] = useState(false);
  const { toast } = useToast();
  const [onboardingData, setOnboardingData] = useState<OnboardingData>({
    vehicleMake: "",
    vehicleModel: "",
    vehicleYear: new Date().getFullYear(),
    vehicleColor: "",
    licensePlate: "",
    licenseNumber: "",
    licenseExpiry: "",
    preferredZones: [],
    workingHours: {
      start: "07:00",
      end: "19:00"
    },
    notifications: {
      hotZones: true,
      weather: true,
      earnings: true
    }
  });

  const updateOnboardingData = (data: Partial<OnboardingData>) => {
    setOnboardingData(prev => ({ ...prev, ...data }));
  };

  const handleNext = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    } else {
      // Complete onboarding
      handleComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = async () => {
    try {
      setIsCompleting(true);

      // Prepare complete onboarding data
      const completeData: OnboardingData = {
        ...userData,
        ...onboardingData
      };

      // Save to database
      const driverProfile = await driverService.createDriverProfile(userId, completeData);

      toast({
        title: "Profil créé avec succès !",
        description: "Bienvenue dans la communauté ChauffeurX !",
      });

      onComplete(driverProfile);
    } catch (error) {
      console.error("Error completing onboarding:", error);
      toast({
        title: "Erreur",
        description: "Impossible de finaliser l'inscription. Veuillez réessayer.",
        variant: "destructive",
      });
    } finally {
      setIsCompleting(false);
    }
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <VehicleInfoStep
            data={onboardingData}
            onUpdate={updateOnboardingData}
            onNext={handleNext}
          />
        );
      case 2:
        return (
          <LicenseVerificationStep
            data={onboardingData}
            onUpdate={updateOnboardingData}
            onNext={handleNext}
          />
        );
      case 3:
        return (
          <ProfilePhotoStep
            userData={userData}
            data={onboardingData}
            onUpdate={updateOnboardingData}
            onNext={handleNext}
          />
        );
      case 4:
        return (
          <PreferencesStep
            data={onboardingData}
            onUpdate={updateOnboardingData}
            onNext={handleNext}
          />
        );
      case 5:
        return (
          <TutorialStep
            onComplete={handleComplete}
          />
        );
      default:
        return null;
    }
  };

  const currentStepData = steps[currentStep - 1];
  const progress = (currentStep / steps.length) * 100;

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 flex items-center justify-center p-4 relative overflow-hidden">

      {/* Modern background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-32 h-32 bg-indigo-200/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-32 right-16 w-40 h-40 bg-cyan-200/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      <Card className="w-full max-w-lg mx-4 shadow-2xl border-0 bg-white/80 backdrop-blur-xl rounded-3xl relative z-10">
        <CardHeader className="pb-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <CardTitle className="text-2xl font-black text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-cyan-600">
                Configuration du profil
              </CardTitle>
              <CardDescription className="text-gray-600 font-medium mt-2">
                Étape {currentStep} sur {steps.length}: {currentStepData.description}
              </CardDescription>
            </div>
            <div className="bg-gradient-to-r from-indigo-100 to-cyan-100 px-4 py-2 rounded-full">
              <span className="text-sm font-bold text-indigo-700">
                {Math.round(progress)}%
              </span>
            </div>
          </div>
          <div className="relative">
            <div className="h-3 bg-gray-200 rounded-full overflow-hidden">
              <div className="h-full bg-gradient-to-r from-indigo-500 to-cyan-500 rounded-full transition-all duration-500 ease-out"
                   style={{ width: `${progress}%` }}></div>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="px-8 pb-8">
          {renderCurrentStep()}

          {currentStep < 5 && (
            <div className="flex justify-between mt-8 gap-4">
              <Button
                variant="outline"
                onClick={handlePrevious}
                disabled={currentStep === 1 || isCompleting}
                className="h-12 px-6 rounded-2xl border-2 border-gray-200 hover:border-gray-300 hover:bg-gray-50 font-semibold transition-all duration-300 transform hover:scale-105 active:scale-95"
              >
                <ChevronLeft className="w-5 h-5 mr-2" />
                Précédent
              </Button>

              <Button
                onClick={handleNext}
                disabled={isCompleting}
                className="h-12 px-8 rounded-2xl bg-gradient-to-r from-indigo-500 to-cyan-500 hover:from-indigo-600 hover:to-cyan-600 font-semibold shadow-xl border-0 transform hover:scale-105 transition-all duration-300 active:scale-95"
              >
                {isCompleting ? (
                  <>
                    <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                    Finalisation...
                  </>
                ) : (
                  <>
                    Suivant
                    <ChevronRight className="w-5 h-5 ml-2" />
                  </>
                )}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
