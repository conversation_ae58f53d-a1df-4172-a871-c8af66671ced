import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { ChevronLeft, ChevronRight, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { driverService, type OnboardingData } from "@/services/driverService";
import VehicleInfoStep from "./VehicleInfoStep";
import LicenseVerificationStep from "./LicenseVerificationStep";
import ProfilePhotoStep from "./ProfilePhotoStep";
import PreferencesStep from "./PreferencesStep";
import TutorialStep from "./TutorialStep";

interface OnboardingFlowProps {
  userData: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
  };
  userId: string;
  onComplete: (driverProfile: any) => void;
}

interface OnboardingData {
  // Vehicle Information
  vehicleMake: string;
  vehicleModel: string;
  vehicleYear: number;
  vehicleColor: string;
  licensePlate: string;
  
  // License Information
  licenseNumber: string;
  licenseExpiry: string;
  licensePhoto?: File;
  
  // Profile
  profilePhoto?: File;
  
  // Preferences
  preferredZones: string[];
  workingHours: {
    start: string;
    end: string;
  };
  notifications: {
    hotZones: boolean;
    weather: boolean;
    earnings: boolean;
  };
}

const steps = [
  {
    id: 1,
    title: "Informations du véhicule",
    description: "Renseignez les détails de votre véhicule"
  },
  {
    id: 2,
    title: "Vérification du permis",
    description: "Téléchargez votre permis de conduire"
  },
  {
    id: 3,
    title: "Photo de profil",
    description: "Ajoutez votre photo de profil"
  },
  {
    id: 4,
    title: "Préférences",
    description: "Configurez vos préférences de travail"
  },
  {
    id: 5,
    title: "Tutoriel",
    description: "Découvrez les fonctionnalités de l'app"
  }
];

export default function OnboardingFlow({ userData, userId, onComplete }: OnboardingFlowProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const [isCompleting, setIsCompleting] = useState(false);
  const { toast } = useToast();
  const [onboardingData, setOnboardingData] = useState<OnboardingData>({
    vehicleMake: "",
    vehicleModel: "",
    vehicleYear: new Date().getFullYear(),
    vehicleColor: "",
    licensePlate: "",
    licenseNumber: "",
    licenseExpiry: "",
    preferredZones: [],
    workingHours: {
      start: "07:00",
      end: "19:00"
    },
    notifications: {
      hotZones: true,
      weather: true,
      earnings: true
    }
  });

  const updateOnboardingData = (data: Partial<OnboardingData>) => {
    setOnboardingData(prev => ({ ...prev, ...data }));
  };

  const handleNext = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    } else {
      // Complete onboarding
      handleComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = async () => {
    try {
      setIsCompleting(true);

      // Prepare complete onboarding data
      const completeData: OnboardingData = {
        ...userData,
        ...onboardingData
      };

      // Save to database
      const driverProfile = await driverService.createDriverProfile(userId, completeData);

      toast({
        title: "Profil créé avec succès !",
        description: "Bienvenue dans la communauté ChauffeurX !",
      });

      onComplete(driverProfile);
    } catch (error) {
      console.error("Error completing onboarding:", error);
      toast({
        title: "Erreur",
        description: "Impossible de finaliser l'inscription. Veuillez réessayer.",
        variant: "destructive",
      });
    } finally {
      setIsCompleting(false);
    }
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <VehicleInfoStep
            data={onboardingData}
            onUpdate={updateOnboardingData}
            onNext={handleNext}
          />
        );
      case 2:
        return (
          <LicenseVerificationStep
            data={onboardingData}
            onUpdate={updateOnboardingData}
            onNext={handleNext}
          />
        );
      case 3:
        return (
          <ProfilePhotoStep
            userData={userData}
            data={onboardingData}
            onUpdate={updateOnboardingData}
            onNext={handleNext}
          />
        );
      case 4:
        return (
          <PreferencesStep
            data={onboardingData}
            onUpdate={updateOnboardingData}
            onNext={handleNext}
          />
        );
      case 5:
        return (
          <TutorialStep
            onComplete={handleComplete}
          />
        );
      default:
        return null;
    }
  };

  const currentStepData = steps[currentStep - 1];
  const progress = (currentStep / steps.length) * 100;

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/10 to-secondary/10 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl">
        <CardHeader>
          <div className="flex items-center justify-between mb-4">
            <div>
              <CardTitle className="text-xl">Configuration du profil</CardTitle>
              <CardDescription>
                Étape {currentStep} sur {steps.length}: {currentStepData.description}
              </CardDescription>
            </div>
            <div className="text-sm text-muted-foreground">
              {Math.round(progress)}%
            </div>
          </div>
          <Progress value={progress} className="w-full" />
        </CardHeader>
        
        <CardContent>
          {renderCurrentStep()}

          {currentStep < 5 && (
            <div className="flex justify-between mt-6">
              <Button
                variant="outline"
                onClick={handlePrevious}
                disabled={currentStep === 1 || isCompleting}
              >
                <ChevronLeft className="w-4 h-4 mr-2" />
                Précédent
              </Button>

              <Button onClick={handleNext} disabled={isCompleting}>
                {isCompleting ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Finalisation...
                  </>
                ) : (
                  <>
                    Suivant
                    <ChevronRight className="w-4 h-4 ml-2" />
                  </>
                )}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
