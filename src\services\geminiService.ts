interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
    };
  }>;
}

interface AIRecommendation {
  id: number;
  zone: string;
  percentage: string;
  timeFrame: string;
  reason: string;
  urgency: 'high' | 'medium' | 'low';
}

class GeminiService {
  private apiKey: string;
  private baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';

  constructor() {
    this.apiKey = import.meta.env.VITE_GEMINI_API_KEY;
    if (!this.apiKey) {
      throw new Error('Gemini API key not found in environment variables');
    }
  }

  async getVTCRecommendations(): Promise<AIRecommendation[]> {
    try {
      const prompt = `En tant qu'expert en transport VTC à Abidjan, Côte d'Ivoire, analyse les tendances actuelles et recommande les 3 meilleures zones pour les chauffeurs VTC. 

Réponds UNIQUEMENT au format JSON suivant, sans texte supplémentaire:
{
  "recommendations": [
    {
      "zone": "nom de la zone",
      "percentage": "+XX%",
      "timeFrame": "période optimale",
      "reason": "raison de la demande",
      "urgency": "high/medium/low"
    }
  ]
}

Zones principales d'Abidjan à considérer: Plateau, Adjamé, Cocody, Zone 4, Marcory, Treichville, Yopougon, Riviera.
Considère les facteurs: heure actuelle, jour de la semaine, événements typiques, trafic, météo.`;

      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-goog-api-key': this.apiKey,
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [
                {
                  text: prompt
                }
              ]
            }
          ]
        })
      });

      if (!response.ok) {
        throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
      }

      const data: GeminiResponse = await response.json();
      
      if (!data.candidates || data.candidates.length === 0) {
        throw new Error('No recommendations received from Gemini API');
      }

      const aiText = data.candidates[0].content.parts[0].text;
      
      // Parse the JSON response from Gemini
      try {
        const jsonMatch = aiText.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
          throw new Error('No valid JSON found in AI response');
        }
        
        const parsedResponse = JSON.parse(jsonMatch[0]);
        
        return parsedResponse.recommendations.map((rec: any, index: number) => ({
          id: index + 1,
          zone: rec.zone,
          percentage: rec.percentage,
          timeFrame: rec.timeFrame,
          reason: rec.reason,
          urgency: rec.urgency
        }));
      } catch (parseError) {
        console.error('Failed to parse AI response:', parseError);
        // Fallback to default recommendations if parsing fails
        return this.getFallbackRecommendations();
      }
    } catch (error) {
      console.error('Error fetching Gemini recommendations:', error);
      // Return fallback recommendations on error
      return this.getFallbackRecommendations();
    }
  }

  async getAITip(): Promise<string> {
    try {
      const prompt = `Donne un conseil court et pratique (maximum 2 phrases) pour un chauffeur VTC à Abidjan. 
      Le conseil doit être spécifique à la ville d'Abidjan et utile pour optimiser les revenus. 
      Réponds directement sans préambule.`;

      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-goog-api-key': this.apiKey,
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [
                {
                  text: prompt
                }
              ]
            }
          ]
        })
      });

      if (!response.ok) {
        throw new Error(`Gemini API error: ${response.status}`);
      }

      const data: GeminiResponse = await response.json();
      
      if (data.candidates && data.candidates.length > 0) {
        return data.candidates[0].content.parts[0].text.trim();
      }
      
      return this.getFallbackTip();
    } catch (error) {
      console.error('Error fetching AI tip:', error);
      return this.getFallbackTip();
    }
  }

  private getFallbackRecommendations(): AIRecommendation[] {
    return [
      {
        id: 1,
        zone: "Plateau",
        percentage: "+38%",
        timeFrame: "20 min",
        reason: "Fin de journée de travail",
        urgency: "high"
      },
      {
        id: 2,
        zone: "Adjamé Marché",
        percentage: "+25%",
        timeFrame: "15 min",
        reason: "Heure de pointe matinale",
        urgency: "medium"
      },
      {
        id: 3,
        zone: "Cocody Riviera",
        percentage: "+32%",
        timeFrame: "30 min",
        reason: "Zone résidentielle active",
        urgency: "medium"
      }
    ];
  }

  private getFallbackTip(): string {
    const tips = [
      "Les vendredis soirs entre 21h-23h, la Zone 4 génère en moyenne 45% de demandes en plus.",
      "Positionnez-vous près des centres commerciaux entre 18h-20h pour maximiser vos courses.",
      "Les matins de semaine, Plateau et Adjamé sont les zones les plus rentables entre 7h-9h."
    ];
    return tips[Math.floor(Math.random() * tips.length)];
  }
}

export const geminiService = new GeminiService();
export type { AIRecommendation };
