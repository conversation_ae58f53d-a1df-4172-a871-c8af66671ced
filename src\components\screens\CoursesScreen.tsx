import { useState, useEffect } from "react";
import { Play, Square, MapPin, Clock, DollarSign, TrendingUp, Navigation, AlertTriangle, Bo<PERSON> } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";

interface Course {
  id: string;
  startTime: Date;
  endTime?: Date;
  startLocation: string;
  endLocation?: string;
  distance?: number;
  estimatedRevenue?: number;
  status: 'active' | 'completed';
}

interface DailySummary {
  totalCourses: number;
  totalRevenue: number;
  totalTime: number; // en minutes
  activeTime: number; // temps en course
}

export default function CoursesScreen() {
  const [activeCourse, setActiveCourse] = useState<Course | null>(null);
  const [todayCourses, setTodayCourses] = useState<Course[]>([]);
  const [location, setLocation] = useState("Cocody Danga");
  const [showSuggestion, setShowSuggestion] = useState(false);
  const { toast } = useToast();

  // Simulation données du jour
  const [dailySummary] = useState<DailySummary>({
    totalCourses: 4,
    totalRevenue: 21500,
    totalTime: 480, // 8h
    activeTime: 200 // 3h20
  });

  // Simulation courses terminées aujourd'hui
  const [completedCourses] = useState([
    { id: '1', startTime: new Date(2024, 6, 10, 8, 30), endTime: new Date(2024, 6, 10, 9, 0), startLocation: 'Plateau', endLocation: 'Adjamé', distance: 8.5, estimatedRevenue: 4500, status: 'completed' as const },
    { id: '2', startTime: new Date(2024, 6, 10, 9, 45), endTime: new Date(2024, 6, 10, 10, 20), startLocation: 'Adjamé', endLocation: 'Cocody', distance: 12.2, estimatedRevenue: 6200, status: 'completed' as const },
    { id: '3', startTime: new Date(2024, 6, 10, 11, 15), endTime: new Date(2024, 6, 10, 11, 45), startLocation: 'Cocody', endLocation: 'Zone 4', distance: 6.8, estimatedRevenue: 3800, status: 'completed' as const },
    { id: '4', startTime: new Date(2024, 6, 10, 14, 20), endTime: new Date(2024, 6, 10, 15, 10), startLocation: 'Zone 4', endLocation: 'Plateau', distance: 11.5, estimatedRevenue: 7000, status: 'completed' as const },
  ]);

  // Simulation suggestion IA après mouvement sans course déclarée
  useEffect(() => {
    // Simule une suggestion après 3 secondes
    const timer = setTimeout(() => {
      setShowSuggestion(true);
    }, 3000);
    return () => clearTimeout(timer);
  }, []);

  const startCourse = () => {
    const newCourse: Course = {
      id: Date.now().toString(),
      startTime: new Date(),
      startLocation: location,
      status: 'active'
    };
    setActiveCourse(newCourse);
    setShowSuggestion(false);
    toast({
      title: "Course démarrée",
      description: `Début de course enregistré à ${location}`,
    });
  };

  const endCourse = () => {
    if (!activeCourse) return;
    
    const endedCourse: Course = {
      ...activeCourse,
      endTime: new Date(),
      endLocation: "Marcory Zone Industrielle", // Simulation
      distance: 9.2, // Simulation GPS
      estimatedRevenue: 5200, // Estimation IA
      status: 'completed'
    };
    
    setTodayCourses(prev => [...prev, endedCourse]);
    setActiveCourse(null);
    
    toast({
      title: "Course terminée",
      description: `Distance: ${endedCourse.distance}km • Revenu estimé: ${endedCourse.estimatedRevenue} CFA`,
    });
  };

  const acceptSuggestion = () => {
    setShowSuggestion(false);
    // Enregistrer rétroactivement la course suggérée
    const retroCourse: Course = {
      id: Date.now().toString(),
      startTime: new Date(Date.now() - 12 * 60 * 1000), // Il y a 12 min
      endTime: new Date(),
      startLocation: "Riviera Palmeraie",
      endLocation: location,
      distance: 3.2,
      estimatedRevenue: 2800,
      status: 'completed'
    };
    setTodayCourses(prev => [...prev, retroCourse]);
    toast({
      title: "Course enregistrée",
      description: "Course rétroactive ajoutée avec succès",
    });
  };

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      {/* Uber-style Header */}
      <div className="bg-white shadow-sm border-b border-gray-100 safe-area-top">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-uber-title text-gray-900">Courses</h1>
              <div className="flex items-center mt-1">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                <p className="text-uber-caption text-gray-600">{location}</p>
              </div>
            </div>
            <div className={cn(
              "px-4 py-2 rounded-full text-sm font-semibold",
              activeCourse
                ? "bg-yellow-100 text-yellow-800"
                : "bg-green-100 text-green-800"
            )}>
              {activeCourse ? 'En course' : 'Disponible'}
            </div>
          </div>
        </div>
      </div>

      <div className="flex-1 px-6 py-4 space-y-6 overflow-y-auto pb-24">

        {/* Uber-style Daily Summary */}
        <div className="card-uber-elevated p-6 bg-gradient-to-r from-yellow-400 to-orange-500 text-white">
          <div className="grid grid-cols-2 gap-6">
            <div>
              <div className="text-sm opacity-90 font-medium">Courses aujourd'hui</div>
              <div className="text-3xl font-bold mt-1">{dailySummary.totalCourses}</div>
            </div>
            <div>
              <div className="text-sm opacity-90 font-medium">Revenu estimé</div>
              <div className="text-3xl font-bold mt-1">{dailySummary.totalRevenue.toLocaleString()} CFA</div>
            </div>
          </div>
          <div className="mt-6 pt-4 border-t border-white/20">
            <div className="flex items-center justify-between">
              <span className="text-sm opacity-90">Temps en course</span>
              <span className="text-sm font-semibold">
                {Math.floor(dailySummary.activeTime / 60)}h{dailySummary.activeTime % 60}min
              </span>
            </div>
          </div>
        </div>

        {/* AI Suggestion - Uber style */}
        {showSuggestion && (
          <div className="card-uber p-5 bg-blue-50 border-l-4 border-blue-500">
            <div className="flex items-start space-x-4">
              <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                <Bot size={20} className="text-white" />
              </div>
              <div className="flex-1">
                <h3 className="text-uber-subtitle text-blue-900 mb-2">Suggestion IA</h3>
                <p className="text-uber-body text-blue-800 mb-4">
                  Tu viens de parcourir 3,2km sans déclarer de course.
                  Était-ce une course de Riviera Palmeraie vers {location} ?
                </p>
                <div className="flex space-x-3">
                  <Button
                    size="sm"
                    onClick={acceptSuggestion}
                    className="btn-uber-primary bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium"
                  >
                    Oui, enregistrer
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setShowSuggestion(false)}
                    className="border-blue-300 text-blue-700 hover:bg-blue-50 px-4 py-2 rounded-lg font-medium"
                  >
                    Non, c'était personnel
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Uber-style Trip Controls */}
        <div className="card-uber-elevated p-8">
          {!activeCourse ? (
            <div className="text-center">
              <div className="w-20 h-20 bg-green-100 rounded-full mx-auto flex items-center justify-center mb-6">
                <Play size={32} className="text-green-600" />
              </div>
              <h3 className="text-uber-subtitle text-gray-900 mb-2">Prêt pour une nouvelle course ?</h3>
              <p className="text-uber-body text-gray-600 mb-6">Appuyez pour commencer à enregistrer votre prochaine course</p>
              <Button
                onClick={startCourse}
                className="w-full h-14 bg-green-600 hover:bg-green-700 text-white rounded-2xl font-semibold text-lg shadow-lg transition-all duration-200 transform hover:scale-105 active:scale-95"
              >
                <Play className="mr-3" size={24} />
                Démarrer une course
              </Button>
            </div>
          ) : (
            <div className="text-center">
              <div className="mb-6">
                <div className="w-20 h-20 bg-yellow-100 rounded-full mx-auto flex items-center justify-center mb-4 relative">
                  <Play size={32} className="text-yellow-600" />
                  <div className="absolute inset-0 bg-yellow-400 rounded-full animate-ping opacity-20"></div>
                </div>
                <h3 className="text-uber-subtitle text-yellow-700 mb-2">Course en cours</h3>
                <div className="bg-yellow-50 rounded-xl p-4 mb-4">
                  <p className="text-sm text-yellow-800 font-medium">
                    Démarrée à {activeCourse.startTime.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}
                  </p>
                  <p className="text-sm text-yellow-700">
                    Depuis {activeCourse.startLocation}
                  </p>
                </div>
              </div>
              <Button
                onClick={endCourse}
                className="w-full h-14 bg-red-600 hover:bg-red-700 text-white rounded-2xl font-semibold text-lg shadow-lg transition-all duration-200 transform hover:scale-105 active:scale-95"
              >
                <Square className="mr-3" size={24} />
                Terminer la course
              </Button>
            </div>
          )}
        </div>

        {/* AI Tip of the Day - Uber style */}
        <div className="card-uber p-5 bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-100">
          <div className="flex items-start space-x-4">
            <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-white text-lg">💡</span>
            </div>
            <div>
              <div className="flex items-center space-x-2 mb-2">
                <span className="text-xs font-bold text-purple-700 uppercase tracking-wide">Astuce IA du jour</span>
              </div>
              <p className="text-uber-body text-gray-700">
                Plateau très actif entre 13h-15h aujourd'hui (+42% demandes).
                Positionnez-vous là-bas dans 30 minutes !
              </p>
            </div>
          </div>
        </div>

        {/* Today's Trips - Uber style */}
        <div className="card-uber p-6">
          <div className="flex items-center space-x-3 mb-6">
            <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
              <Clock size={16} className="text-gray-600" />
            </div>
            <h3 className="text-uber-subtitle text-gray-900">Courses d'aujourd'hui</h3>
          </div>

          <div className="space-y-4">
            {[...completedCourses, ...todayCourses].map((course, index) => (
              <div key={course.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-2xl hover:bg-gray-100 transition-colors">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <div className="text-uber-body font-semibold text-gray-900">
                      {course.startTime.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}
                      {course.endTime && ` - ${course.endTime.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}`}
                    </div>
                    {course.distance && (
                      <div className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                        {course.distance}km
                      </div>
                    )}
                  </div>
                  <div className="text-uber-caption text-gray-600">
                    {course.startLocation} → {course.endLocation || '...'}
                  </div>
                </div>
                <div className="text-right">
                  {course.estimatedRevenue && (
                    <div className="text-lg font-bold text-green-600">
                      {course.estimatedRevenue.toLocaleString()} CFA
                    </div>
                  )}
                  <div className={cn(
                    "text-xs font-medium px-2 py-1 rounded-full",
                    course.status === 'active'
                      ? "bg-yellow-100 text-yellow-800"
                      : "bg-green-100 text-green-800"
                  )}>
                    {course.status === 'active' ? 'En cours' : 'Terminée'}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Stats - Uber style */}
        <div className="grid grid-cols-3 gap-4">
          <div className="card-uber p-4 text-center">
            <div className="text-2xl font-bold text-green-600 mb-1">
              {Math.round(dailySummary.totalRevenue / dailySummary.totalCourses)}
            </div>
            <div className="text-uber-caption text-gray-600">CFA/course</div>
          </div>
          <div className="card-uber p-4 text-center">
            <div className="text-2xl font-bold text-blue-600 mb-1">
              {Math.round(dailySummary.activeTime / dailySummary.totalCourses)}
            </div>
            <div className="text-uber-caption text-gray-600">min/course</div>
          </div>
          <div className="card-uber p-4 text-center">
            <div className="text-2xl font-bold text-purple-600 mb-1">
              {Math.round((dailySummary.activeTime / dailySummary.totalTime) * 100)}%
            </div>
            <div className="text-uber-caption text-gray-600">Efficacité</div>
          </div>
        </div>

      </div>
    </div>
  );
}