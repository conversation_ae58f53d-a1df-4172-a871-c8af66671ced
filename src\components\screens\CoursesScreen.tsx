import { useState, useEffect } from "react";
import { Play, Square, MapPin, Clock, DollarSign, TrendingUp, Navigation, AlertTriangle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";

interface Course {
  id: string;
  startTime: Date;
  endTime?: Date;
  startLocation: string;
  endLocation?: string;
  distance?: number;
  estimatedRevenue?: number;
  status: 'active' | 'completed';
}

interface DailySummary {
  totalCourses: number;
  totalRevenue: number;
  totalTime: number; // en minutes
  activeTime: number; // temps en course
}

export default function CoursesScreen() {
  const [activeCourse, setActiveCourse] = useState<Course | null>(null);
  const [todayCourses, setTodayCourses] = useState<Course[]>([]);
  const [location, setLocation] = useState("Cocody Danga");
  const [showSuggestion, setShowSuggestion] = useState(false);
  const { toast } = useToast();

  // Simulation données du jour
  const [dailySummary] = useState<DailySummary>({
    totalCourses: 4,
    totalRevenue: 21500,
    totalTime: 480, // 8h
    activeTime: 200 // 3h20
  });

  // Simulation courses terminées aujourd'hui
  const [completedCourses] = useState([
    { id: '1', startTime: new Date(2024, 6, 10, 8, 30), endTime: new Date(2024, 6, 10, 9, 0), startLocation: 'Plateau', endLocation: 'Adjamé', distance: 8.5, estimatedRevenue: 4500, status: 'completed' as const },
    { id: '2', startTime: new Date(2024, 6, 10, 9, 45), endTime: new Date(2024, 6, 10, 10, 20), startLocation: 'Adjamé', endLocation: 'Cocody', distance: 12.2, estimatedRevenue: 6200, status: 'completed' as const },
    { id: '3', startTime: new Date(2024, 6, 10, 11, 15), endTime: new Date(2024, 6, 10, 11, 45), startLocation: 'Cocody', endLocation: 'Zone 4', distance: 6.8, estimatedRevenue: 3800, status: 'completed' as const },
    { id: '4', startTime: new Date(2024, 6, 10, 14, 20), endTime: new Date(2024, 6, 10, 15, 10), startLocation: 'Zone 4', endLocation: 'Plateau', distance: 11.5, estimatedRevenue: 7000, status: 'completed' as const },
  ]);

  // Simulation suggestion IA après mouvement sans course déclarée
  useEffect(() => {
    // Simule une suggestion après 3 secondes
    const timer = setTimeout(() => {
      setShowSuggestion(true);
    }, 3000);
    return () => clearTimeout(timer);
  }, []);

  const startCourse = () => {
    const newCourse: Course = {
      id: Date.now().toString(),
      startTime: new Date(),
      startLocation: location,
      status: 'active'
    };
    setActiveCourse(newCourse);
    setShowSuggestion(false);
    toast({
      title: "Course démarrée",
      description: `Début de course enregistré à ${location}`,
    });
  };

  const endCourse = () => {
    if (!activeCourse) return;
    
    const endedCourse: Course = {
      ...activeCourse,
      endTime: new Date(),
      endLocation: "Marcory Zone Industrielle", // Simulation
      distance: 9.2, // Simulation GPS
      estimatedRevenue: 5200, // Estimation IA
      status: 'completed'
    };
    
    setTodayCourses(prev => [...prev, endedCourse]);
    setActiveCourse(null);
    
    toast({
      title: "Course terminée",
      description: `Distance: ${endedCourse.distance}km • Revenu estimé: ${endedCourse.estimatedRevenue} CFA`,
    });
  };

  const acceptSuggestion = () => {
    setShowSuggestion(false);
    // Enregistrer rétroactivement la course suggérée
    const retroCourse: Course = {
      id: Date.now().toString(),
      startTime: new Date(Date.now() - 12 * 60 * 1000), // Il y a 12 min
      endTime: new Date(),
      startLocation: "Riviera Palmeraie",
      endLocation: location,
      distance: 3.2,
      estimatedRevenue: 2800,
      status: 'completed'
    };
    setTodayCourses(prev => [...prev, retroCourse]);
    toast({
      title: "Course enregistrée",
      description: "Course rétroactive ajoutée avec succès",
    });
  };

  return (
    <div className="flex flex-col h-screen bg-background">
      {/* Header */}
      <div className="p-4 bg-card shadow-card">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-lg font-bold">Gestion des Courses</h1>
            <p className="text-sm text-muted-foreground">Vous êtes à {location}</p>
          </div>
          <Badge variant="secondary" className="bg-primary/10 text-primary">
            {activeCourse ? 'En course' : 'Disponible'}
          </Badge>
        </div>
      </div>

      <div className="flex-1 p-4 space-y-4 overflow-y-auto pb-20">
        
        {/* Résumé du jour */}
        <Card className="p-4 bg-gradient-primary text-primary-foreground animate-fade-in-up">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <div className="text-sm opacity-90">Courses aujourd'hui</div>
              <div className="text-2xl font-bold">{dailySummary.totalCourses}</div>
            </div>
            <div>
              <div className="text-sm opacity-90">Revenu estimé</div>
              <div className="text-2xl font-bold">{dailySummary.totalRevenue.toLocaleString()} CFA</div>
            </div>
          </div>
          <div className="mt-3 pt-3 border-t border-primary-foreground/20">
            <div className="flex justify-between text-sm opacity-90">
              <span>Temps total en course : {Math.floor(dailySummary.activeTime / 60)}h{dailySummary.activeTime % 60}min</span>
            </div>
          </div>
        </Card>

        {/* Suggestion IA intelligente */}
        {showSuggestion && (
          <Card className="p-4 bg-warning/10 border-warning/30 animate-fade-in-up">
            <div className="flex items-start space-x-3">
              <AlertTriangle size={20} className="text-warning mt-1" />
              <div className="flex-1">
                <h3 className="font-semibold text-warning">Suggestion IA</h3>
                <p className="text-sm mt-1">
                  Tu viens de parcourir 3,2km sans déclarer de course. 
                  Était-ce une course de Riviera Palmeraie vers {location} ?
                </p>
                <div className="flex space-x-2 mt-3">
                  <Button 
                    size="sm" 
                    onClick={acceptSuggestion}
                    className="bg-warning text-black hover:bg-warning/90"
                  >
                    Oui, enregistrer
                  </Button>
                  <Button 
                    size="sm" 
                    variant="outline" 
                    onClick={() => setShowSuggestion(false)}
                  >
                    Non, c'était personnel
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        )}

        {/* Contrôles de course */}
        <Card className="p-6 animate-fade-in-up" style={{ animationDelay: '0.1s' }}>
          {!activeCourse ? (
            <div className="text-center">
              <h3 className="font-semibold mb-4">Prêt pour une nouvelle course ?</h3>
              <Button 
                onClick={startCourse}
                size="lg"
                className="bg-success text-white hover:bg-success/90 w-full"
              >
                <Play className="mr-2" size={20} />
                Démarrer une course
              </Button>
            </div>
          ) : (
            <div className="text-center">
              <div className="mb-4">
                <div className="w-16 h-16 bg-success/20 rounded-full mx-auto flex items-center justify-center mb-2 animate-hot-pulse">
                  <Play size={24} className="text-success" />
                </div>
                <h3 className="font-semibold text-success">Course en cours</h3>
                <p className="text-sm text-muted-foreground">
                  Démarrée à {activeCourse.startTime.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}
                </p>
                <p className="text-sm text-muted-foreground">
                  Depuis {activeCourse.startLocation}
                </p>
              </div>
              <Button 
                onClick={endCourse}
                size="lg"
                className="bg-hot-zone text-white hover:bg-hot-zone/90 w-full"
              >
                <Square className="mr-2" size={20} />
                Terminer la course
              </Button>
            </div>
          )}
        </Card>

        {/* Astuce IA du jour */}
        <Card className="p-4 bg-secondary/5 border-secondary/20 animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
          <div className="flex items-center space-x-2 mb-2">
            <span className="text-xs font-medium text-secondary">💡 ASTUCE IA DU JOUR</span>
          </div>
          <p className="text-sm">
            Plateau très actif entre 13h-15h aujourd'hui (+42% demandes). 
            Positionnez-vous là-bas dans 30 minutes !
          </p>
        </Card>

        {/* Historique des courses du jour */}
        <Card className="p-4 animate-fade-in-up" style={{ animationDelay: '0.3s' }}>
          <h3 className="font-semibold mb-3 flex items-center space-x-2">
            <Clock size={16} className="text-primary" />
            <span>Courses d'aujourd'hui</span>
          </h3>
          
          <div className="space-y-3">
            {[...completedCourses, ...todayCourses].map((course, index) => (
              <div key={course.id} className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <div className="text-sm font-medium">
                      {course.startTime.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}
                      {course.endTime && ` - ${course.endTime.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}`}
                    </div>
                    {course.distance && (
                      <Badge variant="outline" className="text-xs">
                        {course.distance}km
                      </Badge>
                    )}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {course.startLocation} → {course.endLocation || '...'}
                  </div>
                </div>
                <div className="text-right">
                  {course.estimatedRevenue && (
                    <div className="font-semibold text-success">
                      {course.estimatedRevenue.toLocaleString()} CFA
                    </div>
                  )}
                  <div className="text-xs text-muted-foreground">
                    {course.status === 'active' ? 'En cours' : 'Terminée'}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* Stats rapides */}
        <div className="grid grid-cols-3 gap-3 animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
          <Card className="p-3 text-center">
            <div className="text-xl font-bold text-primary">{Math.round(dailySummary.totalRevenue / dailySummary.totalCourses)}</div>
            <div className="text-xs text-muted-foreground">CFA/course</div>
          </Card>
          <Card className="p-3 text-center">
            <div className="text-xl font-bold text-warning">{Math.round(dailySummary.activeTime / dailySummary.totalCourses)}</div>
            <div className="text-xs text-muted-foreground">min/course</div>
          </Card>
          <Card className="p-3 text-center">
            <div className="text-xl font-bold text-success">{Math.round((dailySummary.activeTime / dailySummary.totalTime) * 100)}%</div>
            <div className="text-xs text-muted-foreground">Efficacité</div>
          </Card>
        </div>

      </div>
    </div>
  );
}