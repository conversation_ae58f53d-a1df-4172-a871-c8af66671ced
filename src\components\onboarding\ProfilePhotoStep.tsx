import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Camera, Upload, CheckCircle, User } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface ProfilePhotoStepProps {
  userData: {
    firstName: string;
    lastName: string;
  };
  data: any;
  onUpdate: (data: any) => void;
  onNext: () => void;
}

export default function ProfilePhotoStep({ userData, data, onUpdate, onNext }: ProfilePhotoStepProps) {
  const [profilePhoto, setProfilePhoto] = useState<File | null>(data.profilePhoto || null);
  const [photoPreview, setPhotoPreview] = useState<string | null>(null);
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'uploading' | 'success'>('idle');
  const { toast } = useToast();

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadStatus('uploading');
      
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setPhotoPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);

      // Simulate upload
      setTimeout(() => {
        setProfilePhoto(file);
        setUploadStatus('success');
        toast({
          title: "Photo téléchargée",
          description: "Votre photo de profil a été téléchargée avec succès.",
        });
      }, 1500);
    }
  };

  const handleNext = () => {
    onUpdate({ profilePhoto });
    onNext();
  };

  const handleSkip = () => {
    onUpdate({ profilePhoto: null });
    onNext();
  };

  const initials = `${userData.firstName.charAt(0)}${userData.lastName.charAt(0)}`.toUpperCase();

  return (
    <div className="space-y-6">
      <div className="text-center">
        <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
          <Camera size={32} className="text-primary" />
        </div>
        <h2 className="text-2xl font-bold mb-2">Photo de profil</h2>
        <p className="text-muted-foreground">
          Ajoutez une photo pour que les clients puissent vous reconnaître
        </p>
      </div>

      <div className="flex flex-col items-center space-y-6">
        <div className="relative">
          <Avatar className="w-32 h-32">
            {photoPreview ? (
              <AvatarImage src={photoPreview} alt="Photo de profil" />
            ) : (
              <AvatarFallback className="text-2xl bg-primary/10 text-primary">
                {initials}
              </AvatarFallback>
            )}
          </Avatar>
          
          {uploadStatus === 'success' && (
            <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
              <CheckCircle className="w-5 h-5 text-white" />
            </div>
          )}
        </div>

        <Card className="w-full max-w-sm p-6 border-dashed border-2 border-muted-foreground/25">
          <div className="text-center">
            {uploadStatus === 'uploading' ? (
              <div className="space-y-2">
                <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto"></div>
                <p className="text-sm">Téléchargement en cours...</p>
              </div>
            ) : (
              <div className="space-y-2">
                <Upload className="w-8 h-8 text-muted-foreground mx-auto" />
                <div>
                  <p className="text-sm font-medium">
                    {profilePhoto ? "Changer la photo" : "Ajouter une photo"}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    PNG, JPG jusqu'à 5MB
                  </p>
                </div>
              </div>
            )}
            <input
              type="file"
              accept="image/*"
              onChange={handleFileUpload}
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            />
          </div>
        </Card>

        <div className="text-center space-y-2">
          <h3 className="font-semibold">Bonjour {userData.firstName} !</h3>
          <p className="text-sm text-muted-foreground">
            Une photo de profil professionnelle augmente la confiance des clients
          </p>
        </div>
      </div>

      <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
        <h3 className="font-semibold text-yellow-900 mb-2">Conseils pour une bonne photo</h3>
        <ul className="text-sm text-yellow-800 space-y-1">
          <li>• Utilisez une photo récente et claire</li>
          <li>• Regardez directement l'objectif</li>
          <li>• Évitez les lunettes de soleil</li>
          <li>• Préférez un arrière-plan neutre</li>
        </ul>
      </div>

      <div className="flex space-x-3">
        <Button variant="outline" onClick={handleSkip} className="flex-1">
          Passer cette étape
        </Button>
        <Button onClick={handleNext} className="flex-1">
          Continuer
        </Button>
      </div>
    </div>
  );
}
