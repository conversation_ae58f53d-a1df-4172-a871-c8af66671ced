import { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Camera, Upload, CheckCircle, User, Image } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface ProfilePhotoStepProps {
  userData: {
    firstName: string;
    lastName: string;
  };
  data: any;
  onUpdate: (data: any) => void;
  onNext: () => void;
}

export default function ProfilePhotoStep({ userData, data, onUpdate, onNext }: ProfilePhotoStepProps) {
  const [profilePhoto, setProfilePhoto] = useState<File | null>(data.profilePhoto || null);
  const [photoPreview, setPhotoPreview] = useState<string | null>(null);
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'uploading' | 'success'>('idle');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: "Erreur",
          description: "Le fichier est trop volumineux. Taille maximale : 5MB.",
          variant: "destructive",
        });
        return;
      }

      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast({
          title: "Erreur",
          description: "Veuillez sélectionner un fichier image (PNG, JPG, JPEG).",
          variant: "destructive",
        });
        return;
      }

      setUploadStatus('uploading');

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setPhotoPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);

      // Simulate upload
      setTimeout(() => {
        setProfilePhoto(file);
        setUploadStatus('success');
        toast({
          title: "Photo téléchargée",
          description: "Votre photo de profil a été téléchargée avec succès.",
        });
      }, 1500);
    }
  };

  const handleTakePhoto = () => {
    if (fileInputRef.current) {
      fileInputRef.current.setAttribute('capture', 'environment');
      fileInputRef.current.click();
    }
  };

  const handleChooseFile = () => {
    if (fileInputRef.current) {
      fileInputRef.current.removeAttribute('capture');
      fileInputRef.current.click();
    }
  };

  const handleRetakePhoto = () => {
    setProfilePhoto(null);
    setPhotoPreview(null);
    setUploadStatus('idle');
  };

  const handleNext = () => {
    onUpdate({ profilePhoto });
    onNext();
  };

  const handleSkip = () => {
    onUpdate({ profilePhoto: null });
    onNext();
  };

  const initials = `${userData.firstName.charAt(0)}${userData.lastName.charAt(0)}`.toUpperCase();

  return (
    <div className="space-y-6 px-2">
      <div className="text-center">
        <div className="w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg">
          <Camera size={36} className="text-white" />
        </div>
        <h2 className="text-uber-title text-gray-900 mb-3">Photo de profil</h2>
        <p className="text-uber-body text-gray-600 max-w-sm mx-auto">
          Ajoutez une photo pour que les clients puissent vous reconnaître
        </p>
      </div>

      <div className="flex flex-col items-center space-y-6">
        <div className="relative">
          <Avatar className="w-32 h-32">
            {photoPreview ? (
              <AvatarImage src={photoPreview} alt="Photo de profil" />
            ) : (
              <AvatarFallback className="text-2xl bg-primary/10 text-primary">
                {initials}
              </AvatarFallback>
            )}
          </Avatar>
          
          {uploadStatus === 'success' && (
            <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
              <CheckCircle className="w-5 h-5 text-white" />
            </div>
          )}
        </div>

        <Card className="w-full max-w-sm p-6 border-dashed border-2 border-muted-foreground/25">
          <div className="text-center">
            {uploadStatus === 'uploading' ? (
              <div className="space-y-2">
                <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto"></div>
                <p className="text-sm">Téléchargement en cours...</p>
              </div>
            ) : (
              <div className="space-y-4">
                <Upload className="w-8 h-8 text-muted-foreground mx-auto" />
                <div>
                  <p className="text-sm font-medium">
                    {profilePhoto ? "Changer la photo" : "Ajouter une photo"}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    PNG, JPG jusqu'à 5MB
                  </p>
                </div>

                {/* Mobile-optimized action buttons */}
                <div className="flex flex-col space-y-3 mt-4">
                  <Button
                    type="button"
                    onClick={handleTakePhoto}
                    className="w-full h-12 bg-blue-600 hover:bg-blue-700 text-white rounded-xl font-medium flex items-center justify-center space-x-2"
                  >
                    <Camera size={20} />
                    <span>Prendre une photo</span>
                  </Button>

                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleChooseFile}
                    className="w-full h-12 border-2 border-gray-300 hover:border-gray-400 rounded-xl font-medium flex items-center justify-center space-x-2"
                  >
                    <Image size={20} />
                    <span>Choisir depuis la galerie</span>
                  </Button>

                  {profilePhoto && (
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleRetakePhoto}
                      className="w-full h-10 border border-red-300 text-red-600 hover:bg-red-50 rounded-xl font-medium"
                    >
                      Supprimer la photo
                    </Button>
                  )}
                </div>
              </div>
            )}
          </div>
        </Card>

        {/* Hidden file input - only triggered by explicit button clicks */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileUpload}
          className="hidden"
          aria-hidden="true"
        />

        <div className="text-center space-y-3">
          <h3 className="text-uber-subtitle text-gray-900">Bonjour {userData.firstName} !</h3>
          <p className="text-uber-body text-gray-600">
            Une photo de profil professionnelle augmente la confiance des clients
          </p>
        </div>
      </div>

      <div className="card-uber p-5 bg-yellow-50 border-l-4 border-yellow-500">
        <h3 className="text-uber-subtitle text-yellow-900 mb-3">Conseils pour une bonne photo</h3>
        <ul className="text-uber-body text-yellow-800 space-y-2">
          <li className="flex items-start space-x-2">
            <span className="text-yellow-600 mt-1">•</span>
            <span>Utilisez une photo récente et claire</span>
          </li>
          <li className="flex items-start space-x-2">
            <span className="text-yellow-600 mt-1">•</span>
            <span>Regardez directement l'objectif</span>
          </li>
          <li className="flex items-start space-x-2">
            <span className="text-yellow-600 mt-1">•</span>
            <span>Évitez les lunettes de soleil</span>
          </li>
          <li className="flex items-start space-x-2">
            <span className="text-yellow-600 mt-1">•</span>
            <span>Préférez un arrière-plan neutre</span>
          </li>
        </ul>
      </div>

      <div className="flex space-x-3">
        <Button
          variant="outline"
          onClick={handleSkip}
          className="flex-1 h-12 border-2 border-gray-300 hover:border-gray-400 rounded-xl font-medium"
        >
          Passer cette étape
        </Button>
        <Button
          onClick={handleNext}
          className="flex-1 h-12 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white rounded-xl font-medium shadow-lg transition-all duration-200 transform hover:scale-105 active:scale-95"
        >
          Continuer
        </Button>
      </div>
    </div>
  );
}
