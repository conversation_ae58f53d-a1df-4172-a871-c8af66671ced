import { useState, useEffect } from "react";
import { Bell, Flame, MapPin, Clock, TrendingUp, Navigation, AlertCircle, Loader2, RefreshCw } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { mapService, type HotZone } from "@/services/mapService";
import { weatherService } from "@/services/weatherService";
import { useToast } from "@/hooks/use-toast";

interface Alert {
  id: number;
  type: 'hot-zone' | 'demand-spike' | 'opportunity' | 'event' | 'weather';
  zone: string;
  message: string;
  timeframe: string;
  intensity: 'high' | 'medium' | 'low';
  timestamp: string;
  icon: any;
}

export default function AlertsScreen() {
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const { toast } = useToast();

  const generateAlertsFromData = async (): Promise<Alert[]> => {
    try {
      const [hotZones, weather] = await Promise.all([
        Promise.resolve(mapService.getHotZones()),
        weatherService.getCurrentWeather()
      ]);

      const generatedAlerts: Alert[] = [];

      // Generate alerts from hot zones
      hotZones.forEach((zone, index) => {
        if (zone.demandLevel > 70) {
          generatedAlerts.push({
            id: index + 1,
            type: 'hot-zone',
            zone: zone.name,
            message: `Zone très active - ${zone.demandLevel}% de demande`,
            timeframe: `Attente: ${zone.estimatedWaitTime}min`,
            intensity: zone.intensity === 'Très chaud' ? 'high' : zone.intensity === 'Chaud' ? 'medium' : 'low',
            timestamp: `${Math.floor(Math.random() * 10) + 1} min`,
            icon: Flame
          });
        }
      });

      // Generate weather alert if needed
      const weatherImpact = weatherService.getWeatherImpactOnDemand(weather);
      if (weatherImpact.includes('hausse') || weatherImpact.includes('forte')) {
        generatedAlerts.push({
          id: generatedAlerts.length + 1,
          type: 'weather',
          zone: 'Toutes zones',
          message: weatherImpact,
          timeframe: 'Maintenant',
          intensity: weatherImpact.includes('forte') ? 'high' : 'medium',
          timestamp: '5 min',
          icon: Clock
        });
      }

      // Add some static alerts for variety
      const staticAlerts: Alert[] = [
        {
          id: generatedAlerts.length + 1,
          type: 'opportunity',
          zone: 'Cocody Riviera',
          message: 'Zone résidentielle recommandée',
          timeframe: '17h-19h',
          intensity: 'medium',
          timestamp: '15 min',
          icon: MapPin
        }
      ];

      return [...generatedAlerts, ...staticAlerts];
    } catch (error) {
      console.error('Error generating alerts:', error);
      return [];
    }
  };

  const fetchAlerts = async () => {
    try {
      setLoading(true);
      const newAlerts = await generateAlertsFromData();
      setAlerts(newAlerts);
    } catch (error) {
      console.error('Error fetching alerts:', error);
      toast({
        title: "Erreur",
        description: "Impossible de charger les alertes.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const refreshAlerts = async () => {
    try {
      setRefreshing(true);
      const newAlerts = await generateAlertsFromData();
      setAlerts(newAlerts);
      toast({
        title: "Alertes mises à jour",
        description: "Nouvelles alertes chargées avec succès.",
      });
    } catch (error) {
      console.error('Error refreshing alerts:', error);
      toast({
        title: "Erreur",
        description: "Impossible de rafraîchir les alertes.",
        variant: "destructive",
      });
    } finally {
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchAlerts();
    // Refresh alerts every 3 minutes
    const interval = setInterval(refreshAlerts, 3 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  const getIntensityColor = (intensity: string) => {
    switch (intensity) {
      case 'high':
        return 'bg-hot-zone/10 text-hot-zone border-hot-zone/20';
      case 'medium':
        return 'bg-warning/10 text-warning border-warning/20';
      default:
        return 'bg-primary/10 text-primary border-primary/20';
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'hot-zone':
        return 'Zone Chaude';
      case 'demand-spike':
        return 'Pic Demande';
      case 'opportunity':
        return 'Opportunité';
      case 'event':
        return 'Événement';
      case 'weather':
        return 'Météo';
      default:
        return 'Alerte';
    }
  };

  return (
    <div className="flex flex-col h-screen bg-background">
      {/* Header */}
      <div className="p-4 bg-card shadow-card">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-primary rounded-full flex items-center justify-center">
              <Bell size={20} className="text-primary-foreground" />
            </div>
            <div>
              <h1 className="text-lg font-bold">Alertes Temps Réel</h1>
              <p className="text-sm text-muted-foreground">
                {loading ? "Chargement..." : "Zones chaudes et opportunités"}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="secondary" className="bg-hot-zone/10 text-hot-zone">
              {loading ? "..." : `${alerts.filter(a => a.intensity === 'high').length} Urgentes`}
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={refreshAlerts}
              disabled={refreshing}
              className="p-1"
            >
              {refreshing ? (
                <Loader2 size={16} className="animate-spin" />
              ) : (
                <RefreshCw size={16} />
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Alerts list */}
      <div className="flex-1 p-4 space-y-3 overflow-y-auto pb-20">

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="flex items-center space-x-3">
              <Loader2 size={24} className="animate-spin text-primary" />
              <span className="text-muted-foreground">Chargement des alertes...</span>
            </div>
          </div>
        ) : alerts.length === 0 ? (
          <div className="text-center py-8">
            <Bell size={48} className="mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">Aucune alerte</h3>
            <p className="text-muted-foreground">Toutes les zones sont calmes pour le moment.</p>
          </div>
        ) : (
          alerts.map((alert, index) => {
          const IconComponent = alert.icon;
          
          return (
            <Card 
              key={alert.id} 
              className={`p-4 animate-fade-in-up ${
                alert.intensity === 'high' ? 'border-l-4 border-l-hot-zone bg-hot-zone/5' : ''
              }`}
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="flex items-start space-x-3">
                
                {/* Icon */}
                <div className={`
                  w-10 h-10 rounded-full flex items-center justify-center
                  ${alert.intensity === 'high' ? 'bg-hot-zone/10' : 'bg-primary/10'}
                  ${alert.intensity === 'high' && 'animate-hot-pulse'}
                `}>
                  <IconComponent 
                    size={20} 
                    className={alert.intensity === 'high' ? 'text-hot-zone' : 'text-primary'} 
                  />
                </div>

                {/* Content */}
                <div className="flex-1">
                  <div className="flex items-start justify-between mb-2">
                    <div>
                      <div className="flex items-center space-x-2 mb-1">
                        <Badge 
                          variant="outline" 
                          className={`text-xs ${getIntensityColor(alert.intensity)}`}
                        >
                          {getTypeLabel(alert.type)}
                        </Badge>
                        <span className="text-xs text-muted-foreground">il y a {alert.timestamp}</span>
                      </div>
                      <h3 className="font-semibold text-base flex items-center space-x-2">
                        <Flame size={16} className="text-hot-zone" />
                        <span>{alert.zone}</span>
                      </h3>
                    </div>
                  </div>

                  <p className="text-sm text-muted-foreground mb-3">
                    {alert.message}
                  </p>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                      <Clock size={12} />
                      <span>{alert.timeframe}</span>
                    </div>
                    
                    <Button 
                      size="sm" 
                      className={`
                        ${alert.intensity === 'high' 
                          ? 'bg-hot-zone text-white hover:bg-hot-zone/90' 
                          : 'bg-primary text-primary-foreground hover:bg-primary/90'
                        }
                      `}
                    >
                      <Navigation size={14} className="mr-1" />
                      Y aller
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          );
        })
        )}

        {/* Summary card */}
        {!loading && alerts.length > 0 && (
        <Card className="p-4 bg-muted/30 animate-fade-in-up" style={{ animationDelay: '0.6s' }}>
          <div className="text-center">
            <h3 className="font-semibold mb-2">Résumé du jour</h3>
            <div className="grid grid-cols-3 gap-4 text-sm">
              <div>
                <div className="text-2xl font-bold text-hot-zone">
                  {alerts.filter(a => a.intensity === 'high').length}
                </div>
                <div className="text-muted-foreground">Zones très chaudes</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-warning">
                  {alerts.filter(a => a.intensity === 'medium').length}
                </div>
                <div className="text-muted-foreground">Opportunités</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-primary">
                  {alerts.length}
                </div>
                <div className="text-muted-foreground">Total alertes</div>
              </div>
            </div>
          </div>
        </Card>
        )}

      </div>
    </div>
  );
}