import { useState } from "react";
import { Bell, Flame, MapPin, Clock, TrendingUp, Navigation, AlertCircle } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

export default function AlertsScreen() {
  const [alerts] = useState([
    {
      id: 1,
      type: 'hot-zone',
      zone: 'Adjamé <PERSON>',
      message: 'Marché très animé ce matin',
      timeframe: '7h-10h',
      intensity: 'high',
      timestamp: '5 min',
      icon: Flame
    },
    {
      id: 2,
      type: 'demand-spike',
      zone: 'Plateau Business',
      message: 'Pic de demandes en cours',
      timeframe: 'Maintenant',
      intensity: 'high',
      timestamp: '2 min',
      icon: TrendingUp
    },
    {
      id: 3,
      type: 'opportunity',
      zone: 'Cocody Riviera',
      message: 'Zone recommandée pour fin d\'après-midi',
      timeframe: '17h-19h',
      intensity: 'medium',
      timestamp: '15 min',
      icon: MapPin
    },
    {
      id: 4,
      type: 'event',
      zone: 'Zone 4 Marcory',
      message: 'Événement sportif - forte affluence prévue',
      timeframe: '19h-22h',
      intensity: 'high',
      timestamp: '30 min',
      icon: AlertCircle
    },
    {
      id: 5,
      type: 'weather',
      zone: 'Toutes zones',
      message: 'Pluie prévue - demandes en hausse',
      timeframe: '14h-16h',
      intensity: 'medium',
      timestamp: '1h',
      icon: Clock
    }
  ]);

  const getIntensityColor = (intensity: string) => {
    switch (intensity) {
      case 'high':
        return 'bg-hot-zone/10 text-hot-zone border-hot-zone/20';
      case 'medium':
        return 'bg-warning/10 text-warning border-warning/20';
      default:
        return 'bg-primary/10 text-primary border-primary/20';
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'hot-zone':
        return 'Zone Chaude';
      case 'demand-spike':
        return 'Pic Demande';
      case 'opportunity':
        return 'Opportunité';
      case 'event':
        return 'Événement';
      case 'weather':
        return 'Météo';
      default:
        return 'Alerte';
    }
  };

  return (
    <div className="flex flex-col h-screen bg-background">
      {/* Header */}
      <div className="p-4 bg-card shadow-card">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-primary rounded-full flex items-center justify-center">
              <Bell size={20} className="text-primary-foreground" />
            </div>
            <div>
              <h1 className="text-lg font-bold">Alertes Temps Réel</h1>
              <p className="text-sm text-muted-foreground">Zones chaudes et opportunités</p>
            </div>
          </div>
          <Badge variant="secondary" className="bg-hot-zone/10 text-hot-zone">
            {alerts.filter(a => a.intensity === 'high').length} Urgentes
          </Badge>
        </div>
      </div>

      {/* Alerts list */}
      <div className="flex-1 p-4 space-y-3 overflow-y-auto pb-20">
        
        {alerts.map((alert, index) => {
          const IconComponent = alert.icon;
          
          return (
            <Card 
              key={alert.id} 
              className={`p-4 animate-fade-in-up ${
                alert.intensity === 'high' ? 'border-l-4 border-l-hot-zone bg-hot-zone/5' : ''
              }`}
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="flex items-start space-x-3">
                
                {/* Icon */}
                <div className={`
                  w-10 h-10 rounded-full flex items-center justify-center
                  ${alert.intensity === 'high' ? 'bg-hot-zone/10' : 'bg-primary/10'}
                  ${alert.intensity === 'high' && 'animate-hot-pulse'}
                `}>
                  <IconComponent 
                    size={20} 
                    className={alert.intensity === 'high' ? 'text-hot-zone' : 'text-primary'} 
                  />
                </div>

                {/* Content */}
                <div className="flex-1">
                  <div className="flex items-start justify-between mb-2">
                    <div>
                      <div className="flex items-center space-x-2 mb-1">
                        <Badge 
                          variant="outline" 
                          className={`text-xs ${getIntensityColor(alert.intensity)}`}
                        >
                          {getTypeLabel(alert.type)}
                        </Badge>
                        <span className="text-xs text-muted-foreground">il y a {alert.timestamp}</span>
                      </div>
                      <h3 className="font-semibold text-base flex items-center space-x-2">
                        <Flame size={16} className="text-hot-zone" />
                        <span>{alert.zone}</span>
                      </h3>
                    </div>
                  </div>

                  <p className="text-sm text-muted-foreground mb-3">
                    {alert.message}
                  </p>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                      <Clock size={12} />
                      <span>{alert.timeframe}</span>
                    </div>
                    
                    <Button 
                      size="sm" 
                      className={`
                        ${alert.intensity === 'high' 
                          ? 'bg-hot-zone text-white hover:bg-hot-zone/90' 
                          : 'bg-primary text-primary-foreground hover:bg-primary/90'
                        }
                      `}
                    >
                      <Navigation size={14} className="mr-1" />
                      Y aller
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          );
        })}

        {/* Summary card */}
        <Card className="p-4 bg-muted/30 animate-fade-in-up" style={{ animationDelay: '0.6s' }}>
          <div className="text-center">
            <h3 className="font-semibold mb-2">Résumé du jour</h3>
            <div className="grid grid-cols-3 gap-4 text-sm">
              <div>
                <div className="text-2xl font-bold text-hot-zone">
                  {alerts.filter(a => a.intensity === 'high').length}
                </div>
                <div className="text-muted-foreground">Zones très chaudes</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-warning">
                  {alerts.filter(a => a.intensity === 'medium').length}
                </div>
                <div className="text-muted-foreground">Opportunités</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-primary">
                  {alerts.length}
                </div>
                <div className="text-muted-foreground">Total alertes</div>
              </div>
            </div>
          </div>
        </Card>

      </div>
    </div>
  );
}