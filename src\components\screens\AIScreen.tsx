import { useState, useEffect } from "react";
import { Bo<PERSON>, MapPin, TrendingUp, Navigation, Clock, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { geminiService, type AIRecommendation } from "@/services/geminiService";
import { useToast } from "@/hooks/use-toast";

export default function AIScreen() {
  const [recommendations, setRecommendations] = useState<AIRecommendation[]>([]);
  const [aiTip, setAiTip] = useState<string>("");
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const { toast } = useToast();

  const fetchRecommendations = async () => {
    try {
      setLoading(true);
      const [recs, tip] = await Promise.all([
        geminiService.getVTCRecommendations(),
        geminiService.getAITip()
      ]);
      setRecommendations(recs);
      setAiTip(tip);
    } catch (error) {
      console.error('Error fetching AI recommendations:', error);
      toast({
        title: "Erreur",
        description: "Impossible de charger les recommandations IA. Données de secours utilisées.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const refreshRecommendations = async () => {
    try {
      setRefreshing(true);
      const [recs, tip] = await Promise.all([
        geminiService.getVTCRecommendations(),
        geminiService.getAITip()
      ]);
      setRecommendations(recs);
      setAiTip(tip);
      toast({
        title: "Recommandations mises à jour",
        description: "Nouvelles recommandations IA chargées avec succès.",
      });
    } catch (error) {
      console.error('Error refreshing recommendations:', error);
      toast({
        title: "Erreur",
        description: "Impossible de rafraîchir les recommandations.",
        variant: "destructive",
      });
    } finally {
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchRecommendations();
  }, []);

  return (
    <div className="flex flex-col h-screen bg-background">
      {/* Header */}
      <div className="p-4 bg-card shadow-card">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-primary rounded-full flex items-center justify-center">
              <Bot size={20} className="text-primary-foreground" />
            </div>
            <div>
              <h1 className="text-lg font-bold">Assistant IA</h1>
              <p className="text-sm text-muted-foreground">Recommandations intelligentes</p>
            </div>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={refreshRecommendations}
            disabled={refreshing}
            className="flex items-center space-x-1"
          >
            {refreshing ? (
              <Loader2 size={16} className="animate-spin" />
            ) : (
              <TrendingUp size={16} />
            )}
            <span>{refreshing ? "Mise à jour..." : "Actualiser"}</span>
          </Button>
        </div>
      </div>

      {/* Chat-like interface */}
      <div className="flex-1 p-4 space-y-4 overflow-y-auto pb-20">

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="flex items-center space-x-3">
              <Loader2 size={24} className="animate-spin text-primary" />
              <span className="text-muted-foreground">Chargement des recommandations IA...</span>
            </div>
          </div>
        ) : (
          <>
            {/* AI Message */}
            <div className="flex items-start space-x-3 animate-fade-in-up">
              <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                <Bot size={16} className="text-primary" />
              </div>
              <Card className="flex-1 p-4 bg-card">
                <p className="text-sm mb-3">
                  Bonjour ! Basé sur l'analyse des données temps réel et les tendances historiques,
                  voici mes recommandations pour optimiser vos revenus :
                </p>
              </Card>
            </div>

        {/* Recommendations */}
        {recommendations.map((rec, index) => (
          <div 
            key={rec.id} 
            className="flex items-start space-x-3 animate-fade-in-up"
            style={{ animationDelay: `${index * 0.2}s` }}
          >
            <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
              <MapPin size={16} className="text-primary" />
            </div>
            <Card className="flex-1 p-4">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-semibold text-lg">{rec.zone}</h3>
                <div className={`
                  px-2 py-1 rounded-full text-xs font-medium
                  ${rec.urgency === 'high' ? 'bg-hot-zone/10 text-hot-zone' : 'bg-warning/10 text-warning'}
                `}>
                  {rec.percentage} demandes
                </div>
              </div>
              
              <div className="flex items-center space-x-4 text-sm text-muted-foreground mb-3">
                <div className="flex items-center space-x-1">
                  <Clock size={14} />
                  <span>{rec.timeFrame}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <TrendingUp size={14} />
                  <span>{rec.reason}</span>
                </div>
              </div>

              <Button 
                className="w-full bg-primary text-primary-foreground hover:bg-primary/90"
                size="sm"
              >
                <Navigation className="mr-2" size={16} />
                Naviguer vers la zone
              </Button>
            </Card>
          </div>
        ))}

            {/* AI Tip */}
            {aiTip && (
              <div className="flex items-start space-x-3 animate-fade-in-up" style={{ animationDelay: '0.6s' }}>
                <div className="w-8 h-8 bg-secondary/10 rounded-full flex items-center justify-center">
                  <Bot size={16} className="text-secondary" />
                </div>
                <Card className="flex-1 p-4 bg-secondary/5 border-secondary/20">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-xs font-medium text-secondary">💡 ASTUCE IA</span>
                  </div>
                  <p className="text-sm">
                    {aiTip}
                  </p>
                </Card>
              </div>
            )}
          </>
        )}

      </div>
    </div>
  );
}