import { useState } from "react";
import BottomNavigation from "@/components/BottomNavigation";
import MapScreen from "@/components/screens/MapScreen";
import CoursesScreen from "@/components/screens/CoursesScreen";
import AIScreen from "@/components/screens/AIScreen";
import EarningsScreen from "@/components/screens/EarningsScreen";
import AlertsScreen from "@/components/screens/AlertsScreen";
import ProfileScreen from "@/components/screens/ProfileScreen";

export default function ChauffeurXApp() {
  const [activeTab, setActiveTab] = useState('courses');

  const renderScreen = () => {
    switch (activeTab) {
      case 'map':
        return <MapScreen />;
      case 'courses':
        return <CoursesScreen />;
      case 'ai':
        return <AIScreen />;
      case 'earnings':
        return <EarningsScreen />;
      case 'alerts':
        return <AlertsScreen />;
      case 'profile':
        return <ProfileScreen />;
      default:
        return <CoursesScreen />;
    }
  };

  return (
    <div className="min-h-screen bg-background font-sans">
      {/* Main Content */}
      <div className="pb-16">
        {renderScreen()}
      </div>
      
      {/* Bottom Navigation */}
      <BottomNavigation 
        activeTab={activeTab} 
        onTabChange={setActiveTab} 
      />
    </div>
  );
}