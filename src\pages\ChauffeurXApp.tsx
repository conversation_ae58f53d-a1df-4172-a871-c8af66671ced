import { useState, useEffect } from "react";
import BottomNavigation from "@/components/BottomNavigation";
import MapScreen from "@/components/screens/MapScreen";
import CoursesScreen from "@/components/screens/CoursesScreen";
import AIScreen from "@/components/screens/AIScreen";
import EarningsScreen from "@/components/screens/EarningsScreen";
import AlertsScreen from "@/components/screens/AlertsScreen";
import ProfileScreen from "@/components/screens/ProfileScreen";

export default function ChauffeurXApp() {
  const [activeTab, setActiveTab] = useState('courses');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate app initialization
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  const renderScreen = () => {
    switch (activeTab) {
      case 'map':
        return <MapScreen />;
      case 'courses':
        return <CoursesScreen />;
      case 'ai':
        return <AIScreen />;
      case 'earnings':
        return <EarningsScreen />;
      case 'alerts':
        return <AlertsScreen />;
      case 'profile':
        return <ProfileScreen />;
      default:
        return <CoursesScreen />;
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-yellow-50 to-orange-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-20 h-20 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-2xl animate-pulse">
            <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.22.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.5 16c-.83 0-1.5-.67-1.5-1.5S5.67 13 6.5 13s1.5.67 1.5 1.5S7.33 16 6.5 16zm11 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM5 11l1.5-4.5h11L19 11H5z"/>
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-800 mb-2">ChauffeurX</h2>
          <p className="text-gray-600">Chargement de votre application...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 font-sans antialiased">
      {/* Safe area for notched devices */}
      <div className="safe-area-top"></div>

      {/* Main Content */}
      <div className="pb-20 safe-area-bottom">
        <div className="transition-all duration-300 ease-in-out">
          {renderScreen()}
        </div>
      </div>

      {/* Bottom Navigation */}
      <BottomNavigation
        activeTab={activeTab}
        onTabChange={setActiveTab}
      />
    </div>
  );
}