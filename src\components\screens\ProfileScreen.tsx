import { useState, useEffect } from "react";
import { <PERSON>r, Star, MapPin, Settings, HelpCircle, LogOut, Car, Calendar, Award, Loader2 } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { useSession, signOut } from "@/lib/mockAuth";
import { driverService, type DriverProfile } from "@/services/driverService";
import { useToast } from "@/hooks/use-toast";

export default function ProfileScreen() {
  const [isOnline, setIsOnline] = useState(true);
  const [driverProfile, setDriverProfile] = useState<DriverProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const { data: session } = useSession();
  const { toast } = useToast();

  useEffect(() => {
    const fetchDriverProfile = async () => {
      if (session?.user?.id) {
        try {
          const profile = await driverService.getDriverProfile(session.user.id);
          setDriverProfile(profile);
          if (profile) {
            setIsOnline(profile.isOnline);
          }
        } catch (error) {
          console.error('Error fetching driver profile:', error);
          toast({
            title: "Erreur",
            description: "Impossible de charger le profil.",
            variant: "destructive",
          });
        } finally {
          setLoading(false);
        }
      }
    };

    fetchDriverProfile();
  }, [session, toast]);

  const handleStatusToggle = async () => {
    if (session?.user?.id && driverProfile) {
      try {
        const newStatus = !isOnline;
        await driverService.updateDriverStatus(session.user.id, newStatus);
        setIsOnline(newStatus);
        toast({
          title: newStatus ? "En ligne" : "Hors ligne",
          description: newStatus ? "Vous êtes maintenant disponible pour les courses." : "Vous n'êtes plus disponible.",
        });
      } catch (error) {
        toast({
          title: "Erreur",
          description: "Impossible de changer le statut.",
          variant: "destructive",
        });
      }
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      toast({
        title: "Déconnexion",
        description: "Vous avez été déconnecté avec succès.",
      });
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Erreur lors de la déconnexion.",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-2 text-primary" />
          <p className="text-sm text-muted-foreground">Chargement du profil...</p>
        </div>
      </div>
    );
  }

  if (!driverProfile) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <User className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
          <h3 className="text-lg font-semibold mb-2">Profil non trouvé</h3>
          <p className="text-sm text-muted-foreground">Impossible de charger les informations du profil.</p>
        </div>
      </div>
    );
  }

  const menuItems = [
    {
      icon: Settings,
      label: "Paramètres",
      description: "Notifications, langues, préférences",
      color: "text-muted-foreground"
    },
    {
      icon: HelpCircle,
      label: "Assistance",
      description: "FAQ, support, signaler un problème",
      color: "text-muted-foreground"
    },
    {
      icon: Award,
      label: "Récompenses",
      description: "Badges, défis, bonus fidélité",
      color: "text-warning"
    },
    {
      icon: MapPin,
      label: "Historique des zones",
      description: "Vos zones de travail préférées",
      color: "text-primary"
    }
  ];

  return (
    <div className="flex flex-col h-screen bg-background">
      {/* Header */}
      <div className="p-4 bg-card shadow-card">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-primary rounded-full flex items-center justify-center">
            <User size={20} className="text-primary-foreground" />
          </div>
          <div>
            <h1 className="text-lg font-bold">Mon Profil</h1>
            <p className="text-sm text-muted-foreground">Chauffeur ChauffeurX</p>
          </div>
        </div>
      </div>

      <div className="flex-1 p-4 space-y-4 overflow-y-auto pb-20">
        
        {/* Profile Card */}
        <Card className="p-6 animate-fade-in-up">
          <div className="flex items-center space-x-4 mb-4">
            <Avatar className="w-16 h-16">
              <AvatarImage src={driverProfile.profileImage || "/placeholder-avatar.jpg"} />
              <AvatarFallback className="bg-primary text-primary-foreground text-lg font-bold">
                {driverProfile.firstName.charAt(0)}{driverProfile.lastName.charAt(0)}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <h2 className="text-xl font-bold">{driverProfile.firstName} {driverProfile.lastName}</h2>
              <p className="text-sm text-muted-foreground">{driverProfile.phone}</p>
              <div className="flex items-center space-x-1 mt-1">
                <Star size={16} className="text-warning fill-warning" />
                <span className="font-semibold">{driverProfile.averageRating}</span>
                <span className="text-sm text-muted-foreground">
                  ({driverProfile.totalRides} courses)
                </span>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center space-x-2">
              <Calendar size={16} className="text-muted-foreground" />
              <div>
                <div className="text-muted-foreground">Membre depuis</div>
                <div className="font-medium">{new Date(driverProfile.createdAt).toLocaleDateString('fr-FR', { month: 'long', year: 'numeric' })}</div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Car size={16} className="text-muted-foreground" />
              <div>
                <div className="text-muted-foreground">Véhicule</div>
                <div className="font-medium">{driverProfile.vehicleMake} {driverProfile.vehicleModel} {driverProfile.vehicleYear}</div>
              </div>
            </div>
          </div>
        </Card>

        {/* Stats Cards */}
        <div className="grid grid-cols-3 gap-3">
          <Card className="p-3 text-center animate-fade-in-up" style={{ animationDelay: '0.1s' }}>
            <div className="text-2xl font-bold text-primary">{driverProfile.totalRides}</div>
            <div className="text-xs text-muted-foreground">Courses totales</div>
          </Card>
          <Card className="p-3 text-center animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
            <div className="text-2xl font-bold text-warning">{driverProfile.averageRating}</div>
            <div className="text-xs text-muted-foreground">Note moyenne</div>
          </Card>
          <Card className="p-3 text-center animate-fade-in-up" style={{ animationDelay: '0.3s' }}>
            <div className="text-2xl font-bold text-success">98%</div>
            <div className="text-xs text-muted-foreground">Taux d'acceptation</div>
          </Card>
        </div>

        {/* Favorite Zones */}
        <Card className="p-4 animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
          <h3 className="font-semibold mb-3 flex items-center space-x-2">
            <MapPin size={16} className="text-primary" />
            <span>Zones préférées</span>
          </h3>
          <div className="flex flex-wrap gap-2">
            {profile.favoriteZones.map((zone, index) => (
              <Badge 
                key={zone} 
                variant="secondary" 
                className="bg-primary/10 text-primary"
              >
                {zone}
              </Badge>
            ))}
          </div>
        </Card>

        {/* Menu Items */}
        <div className="space-y-3">
          {menuItems.map((item, index) => {
            const IconComponent = item.icon;
            return (
              <Card 
                key={item.label} 
                className="p-4 hover:bg-muted/50 transition-colors cursor-pointer animate-fade-in-up"
                style={{ animationDelay: `${0.5 + index * 0.1}s` }}
              >
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-muted/50 rounded-full flex items-center justify-center">
                    <IconComponent size={20} className={item.color} />
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">{item.label}</div>
                    <div className="text-sm text-muted-foreground">{item.description}</div>
                  </div>
                </div>
              </Card>
            );
          })}
        </div>

        {/* Logout Button */}
        <Card className="p-4 bg-destructive/5 border-destructive/20 animate-fade-in-up" style={{ animationDelay: '0.9s' }}>
          <Button
            variant="ghost"
            className="w-full text-destructive hover:bg-destructive/10"
            onClick={handleSignOut}
          >
            <LogOut size={16} className="mr-2" />
            Se déconnecter
          </Button>
        </Card>

      </div>
    </div>
  );
}