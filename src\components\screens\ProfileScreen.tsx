import { useState } from "react";
import { <PERSON>r, Star, MapPin, Settings, HelpCircle, LogOut, Car, Calendar, Award } from "lucide-react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";

export default function ProfileScreen() {
  const [profile] = useState({
    name: "<PERSON><PERSON><PERSON>",
    phone: "+225 07 XX XX XX XX",
    rating: 4.8,
    totalRides: 1247,
    memberSince: "Janvier 2023",
    vehicle: "Toyota Yaris 2019",
    favoriteZones: ["Plateau", "Cocody", "Adjamé"]
  });

  const menuItems = [
    {
      icon: Settings,
      label: "Paramètres",
      description: "Notifications, langues, préférences",
      color: "text-muted-foreground"
    },
    {
      icon: HelpCircle,
      label: "Assistance",
      description: "FAQ, support, signaler un problème",
      color: "text-muted-foreground"
    },
    {
      icon: Award,
      label: "Récompenses",
      description: "Badges, défis, bonus fidélité",
      color: "text-warning"
    },
    {
      icon: MapPin,
      label: "Historique des zones",
      description: "Vos zones de travail préférées",
      color: "text-primary"
    }
  ];

  return (
    <div className="flex flex-col h-screen bg-background">
      {/* Header */}
      <div className="p-4 bg-card shadow-card">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-primary rounded-full flex items-center justify-center">
            <User size={20} className="text-primary-foreground" />
          </div>
          <div>
            <h1 className="text-lg font-bold">Mon Profil</h1>
            <p className="text-sm text-muted-foreground">Chauffeur ChauffeurX</p>
          </div>
        </div>
      </div>

      <div className="flex-1 p-4 space-y-4 overflow-y-auto pb-20">
        
        {/* Profile Card */}
        <Card className="p-6 animate-fade-in-up">
          <div className="flex items-center space-x-4 mb-4">
            <Avatar className="w-16 h-16">
              <AvatarImage src="/placeholder-avatar.jpg" />
              <AvatarFallback className="bg-primary text-primary-foreground text-lg font-bold">
                KJ
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <h2 className="text-xl font-bold">{profile.name}</h2>
              <p className="text-sm text-muted-foreground">{profile.phone}</p>
              <div className="flex items-center space-x-1 mt-1">
                <Star size={16} className="text-warning fill-warning" />
                <span className="font-semibold">{profile.rating}</span>
                <span className="text-sm text-muted-foreground">
                  ({profile.totalRides} courses)
                </span>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center space-x-2">
              <Calendar size={16} className="text-muted-foreground" />
              <div>
                <div className="text-muted-foreground">Membre depuis</div>
                <div className="font-medium">{profile.memberSince}</div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Car size={16} className="text-muted-foreground" />
              <div>
                <div className="text-muted-foreground">Véhicule</div>
                <div className="font-medium">{profile.vehicle}</div>
              </div>
            </div>
          </div>
        </Card>

        {/* Stats Cards */}
        <div className="grid grid-cols-3 gap-3">
          <Card className="p-3 text-center animate-fade-in-up" style={{ animationDelay: '0.1s' }}>
            <div className="text-2xl font-bold text-primary">{profile.totalRides}</div>
            <div className="text-xs text-muted-foreground">Courses totales</div>
          </Card>
          <Card className="p-3 text-center animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
            <div className="text-2xl font-bold text-warning">{profile.rating}</div>
            <div className="text-xs text-muted-foreground">Note moyenne</div>
          </Card>
          <Card className="p-3 text-center animate-fade-in-up" style={{ animationDelay: '0.3s' }}>
            <div className="text-2xl font-bold text-success">98%</div>
            <div className="text-xs text-muted-foreground">Taux d'acceptation</div>
          </Card>
        </div>

        {/* Favorite Zones */}
        <Card className="p-4 animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
          <h3 className="font-semibold mb-3 flex items-center space-x-2">
            <MapPin size={16} className="text-primary" />
            <span>Zones préférées</span>
          </h3>
          <div className="flex flex-wrap gap-2">
            {profile.favoriteZones.map((zone, index) => (
              <Badge 
                key={zone} 
                variant="secondary" 
                className="bg-primary/10 text-primary"
              >
                {zone}
              </Badge>
            ))}
          </div>
        </Card>

        {/* Menu Items */}
        <div className="space-y-3">
          {menuItems.map((item, index) => {
            const IconComponent = item.icon;
            return (
              <Card 
                key={item.label} 
                className="p-4 hover:bg-muted/50 transition-colors cursor-pointer animate-fade-in-up"
                style={{ animationDelay: `${0.5 + index * 0.1}s` }}
              >
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-muted/50 rounded-full flex items-center justify-center">
                    <IconComponent size={20} className={item.color} />
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">{item.label}</div>
                    <div className="text-sm text-muted-foreground">{item.description}</div>
                  </div>
                </div>
              </Card>
            );
          })}
        </div>

        {/* Logout Button */}
        <Card className="p-4 bg-destructive/5 border-destructive/20 animate-fade-in-up" style={{ animationDelay: '0.9s' }}>
          <Button 
            variant="ghost" 
            className="w-full text-destructive hover:bg-destructive/10"
          >
            <LogOut size={16} className="mr-2" />
            Se déconnecter
          </Button>
        </Card>

      </div>
    </div>
  );
}