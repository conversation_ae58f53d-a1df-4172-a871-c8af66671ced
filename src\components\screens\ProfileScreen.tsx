import { useState } from "react";
import { <PERSON>r, <PERSON><PERSON><PERSON>, Bell, HelpCircle, LogOut, Edit, Camera, Star, Award, TrendingUp, Shield, Car, CreditCard, FileText, Phone, ChevronRight, MapPin, Clock, DollarSign, ArrowLeft, Check, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/hooks/use-toast";

type ProfileView = 'main' | 'edit' | 'vehicle' | 'documents' | 'settings' | 'notifications' | 'help' | 'earnings';

export default function ProfileScreen() {
  const [currentView, setCurrentView] = useState<ProfileView>('main');
  const [driverData] = useState({
    name: "<PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "+225 07 12 34 56 78",
    rating: 4.8,
    totalRides: 1247,
    joinDate: "Mars 2023",
    address: "Cocody, Abidjan",
    emergencyContact: "+225 05 67 89 01 23",
    vehicleInfo: {
      make: "Toyota",
      model: "Corolla",
      year: 2020,
      plate: "AB 1234 CI",
      color: "Blanc",
      vtcCompany: "Yango"
    },
    documents: {
      license: { status: "Vérifié", expiry: "2026-03-15" },
      insurance: { status: "Vérifié", expiry: "2025-12-31" },
      registration: { status: "Vérifié", expiry: "2025-08-20" },
      vtcPermit: { status: "Vérifié", expiry: "2025-11-10" }
    },
    stats: {
      totalEarnings: 2450000,
      thisMonth: 185000,
      completionRate: 96,
      responseTime: "2.3 min",
      weeklyHours: 42,
      avgRating: 4.8
    }
  });

  const { toast } = useToast();

  const menuItems = [
    { icon: Edit, label: "Modifier le profil", view: 'edit' as ProfileView },
    { icon: Car, label: "Véhicule", view: 'vehicle' as ProfileView },
    { icon: FileText, label: "Documents", view: 'documents' as ProfileView },
    { icon: DollarSign, label: "Revenus détaillés", view: 'earnings' as ProfileView },
    { icon: Settings, label: "Paramètres", view: 'settings' as ProfileView },
    { icon: Bell, label: "Notifications", view: 'notifications' as ProfileView },
    { icon: HelpCircle, label: "Aide & Support", view: 'help' as ProfileView },
    { icon: LogOut, label: "Déconnexion", action: () => toast({ title: "Déconnexion", description: "À bientôt !" }), danger: true }
  ];

  const renderHeader = (title: string, showBack: boolean = false) => (
    <div className="bg-white shadow-sm border-b border-gray-100 safe-area-top">
      <div className="px-6 py-4 flex items-center">
        {showBack && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setCurrentView('main')}
            className="mr-3 p-2"
          >
            <ArrowLeft size={20} />
          </Button>
        )}
        <h1 className="text-uber-title text-gray-900">{title}</h1>
      </div>
    </div>
  );

  const renderMainProfile = () => (
    <div className="flex flex-col min-h-screen bg-gray-50">
      {renderHeader("Mon Profil")}
      
      <div className="flex-1 px-6 py-4 space-y-6 overflow-y-auto pb-24">
        {/* Profile Card */}
        <div className="card-uber-elevated p-6 bg-gradient-to-r from-yellow-400 to-orange-500 text-white">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Avatar className="w-20 h-20 border-4 border-white/20">
                <AvatarImage src="/placeholder-avatar.jpg" />
                <AvatarFallback className="text-2xl font-bold bg-white/20">
                  {driverData.name.split(' ').map(n => n[0]).join('')}
                </AvatarFallback>
              </Avatar>
              <Button
                size="sm"
                className="absolute -bottom-2 -right-2 w-8 h-8 rounded-full bg-white text-orange-500 p-0 hover:bg-gray-100"
              >
                <Camera size={14} />
              </Button>
            </div>
            
            <div className="flex-1">
              <h2 className="text-xl font-bold">{driverData.name}</h2>
              <p className="text-sm opacity-90">{driverData.email}</p>
              <p className="text-sm opacity-90">{driverData.phone}</p>
              
              <div className="flex items-center space-x-2 mt-2">
                <div className="flex items-center space-x-1">
                  <Star size={16} className="text-yellow-300 fill-current" />
                  <span className="font-semibold">{driverData.rating}</span>
                </div>
                <span className="text-sm opacity-75">•</span>
                <span className="text-sm opacity-90">{driverData.totalRides} courses</span>
                <span className="text-sm opacity-75">•</span>
                <span className="text-sm opacity-90">Depuis {driverData.joinDate}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 gap-4">
          <div className="card-uber p-4 text-center">
            <div className="text-2xl font-bold text-green-600 mb-1">
              {driverData.stats.thisMonth.toLocaleString()} CFA
            </div>
            <div className="text-uber-caption text-gray-600">Ce mois-ci</div>
          </div>
          <div className="card-uber p-4 text-center">
            <div className="text-2xl font-bold text-blue-600 mb-1">
              {driverData.stats.completionRate}%
            </div>
            <div className="text-uber-caption text-gray-600">Taux de réussite</div>
          </div>
        </div>

        {/* Menu Items */}
        <div className="space-y-2">
          {menuItems.map((item, index) => (
            <div
              key={index}
              className={`card-uber p-4 flex items-center justify-between cursor-pointer hover:bg-gray-50 transition-colors ${
                item.danger ? 'border-red-200' : ''
              }`}
              onClick={() => {
                if (item.action) {
                  item.action();
                } else if (item.view) {
                  setCurrentView(item.view);
                }
              }}
            >
              <div className="flex items-center space-x-3">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                  item.danger ? 'bg-red-100' : 'bg-gray-100'
                }`}>
                  <item.icon size={20} className={item.danger ? 'text-red-600' : 'text-gray-600'} />
                </div>
                <span className={`text-uber-body ${item.danger ? 'text-red-600' : 'text-gray-900'}`}>
                  {item.label}
                </span>
              </div>
              {!item.action && <ChevronRight size={20} className="text-gray-400" />}
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderEditProfile = () => (
    <div className="flex flex-col min-h-screen bg-gray-50">
      {renderHeader("Modifier le profil", true)}
      
      <div className="flex-1 px-6 py-4 space-y-6 overflow-y-auto pb-24">
        <div className="card-uber p-6 space-y-4">
          <div>
            <Label htmlFor="name">Nom complet</Label>
            <Input id="name" defaultValue={driverData.name} className="input-uber" />
          </div>
          
          <div>
            <Label htmlFor="email">Email</Label>
            <Input id="email" type="email" defaultValue={driverData.email} className="input-uber" />
          </div>
          
          <div>
            <Label htmlFor="phone">Téléphone</Label>
            <Input id="phone" defaultValue={driverData.phone} className="input-uber" />
          </div>
          
          <div>
            <Label htmlFor="address">Adresse</Label>
            <Input id="address" defaultValue={driverData.address} className="input-uber" />
          </div>
          
          <div>
            <Label htmlFor="emergency">Contact d'urgence</Label>
            <Input id="emergency" defaultValue={driverData.emergencyContact} className="input-uber" />
          </div>
          
          <div className="flex space-x-3 pt-4">
            <Button className="flex-1 btn-uber-primary">
              Sauvegarder
            </Button>
            <Button 
              variant="outline" 
              className="flex-1"
              onClick={() => setCurrentView('main')}
            >
              Annuler
            </Button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderVehicleInfo = () => (
    <div className="flex flex-col min-h-screen bg-gray-50">
      {renderHeader("Informations véhicule", true)}
      
      <div className="flex-1 px-6 py-4 space-y-6 overflow-y-auto pb-24">
        <div className="card-uber p-6">
          <div className="flex items-center space-x-3 mb-4">
            <Car className="w-6 h-6 text-blue-600" />
            <h3 className="text-uber-subtitle">Véhicule principal</h3>
          </div>
          
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">Marque:</span>
              <span className="font-medium">{driverData.vehicleInfo.make}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Modèle:</span>
              <span className="font-medium">{driverData.vehicleInfo.model}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Année:</span>
              <span className="font-medium">{driverData.vehicleInfo.year}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Plaque:</span>
              <span className="font-medium">{driverData.vehicleInfo.plate}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Couleur:</span>
              <span className="font-medium">{driverData.vehicleInfo.color}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Compagnie VTC:</span>
              <span className="font-medium">{driverData.vehicleInfo.vtcCompany}</span>
            </div>
          </div>
          
          <Button className="w-full mt-6 btn-uber-primary">
            Modifier les informations
          </Button>
        </div>
      </div>
    </div>
  );

  const renderDocuments = () => (
    <div className="flex flex-col min-h-screen bg-gray-50">
      {renderHeader("Documents", true)}

      <div className="flex-1 px-6 py-4 space-y-4 overflow-y-auto pb-24">
        {Object.entries(driverData.documents).map(([key, doc]) => (
          <div key={key} className="card-uber p-5">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                  <FileText className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <h4 className="text-uber-body font-semibold text-gray-900 capitalize">
                    {key.replace(/([A-Z])/g, ' $1')}
                  </h4>
                  <p className="text-uber-caption text-gray-600">Expire le {doc.expiry}</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Check className="w-5 h-5 text-green-600" />
                <Badge className="bg-green-100 text-green-800 px-3 py-1 rounded-full font-medium">
                  {doc.status}
                </Badge>
              </div>
            </div>

            {/* Action buttons for document management */}
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="flex space-x-3">
                <Button
                  variant="outline"
                  size="sm"
                  className="flex-1 h-10 border-gray-300 hover:border-gray-400 rounded-lg"
                  onClick={() => toast({
                    title: "Voir le document",
                    description: `Affichage de ${key.replace(/([A-Z])/g, ' $1')}`
                  })}
                >
                  Voir
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex-1 h-10 border-blue-300 text-blue-700 hover:bg-blue-50 rounded-lg"
                  onClick={() => toast({
                    title: "Renouveler le document",
                    description: `Renouvellement de ${key.replace(/([A-Z])/g, ' $1')}`
                  })}
                >
                  Renouveler
                </Button>
              </div>
            </div>
          </div>
        ))}

        {/* Add new document section */}
        <div className="card-uber p-5 border-2 border-dashed border-gray-300">
          <div className="text-center">
            <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <FileText className="w-6 h-6 text-gray-500" />
            </div>
            <h4 className="text-uber-body font-medium text-gray-900 mb-2">
              Ajouter un document
            </h4>
            <p className="text-uber-caption text-gray-600 mb-4">
              Téléchargez des documents supplémentaires si nécessaire
            </p>
            <Button
              variant="outline"
              className="w-full h-12 border-2 border-gray-300 hover:border-gray-400 rounded-xl"
              onClick={() => toast({
                title: "Ajouter un document",
                description: "Fonctionnalité en développement"
              })}
            >
              Ajouter un document
            </Button>
          </div>
        </div>
      </div>
    </div>
  );

  switch (currentView) {
    case 'edit':
      return renderEditProfile();
    case 'vehicle':
      return renderVehicleInfo();
    case 'documents':
      return renderDocuments();
    case 'settings':
    case 'notifications':
    case 'help':
    case 'earnings':
      return (
        <div className="flex flex-col min-h-screen bg-gray-50">
          {renderHeader(currentView === 'settings' ? 'Paramètres' : 
                       currentView === 'notifications' ? 'Notifications' :
                       currentView === 'help' ? 'Aide & Support' : 'Revenus détaillés', true)}
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <h3 className="text-lg font-medium text-gray-900 mb-2">Fonctionnalité en développement</h3>
              <p className="text-gray-600">Cette section sera bientôt disponible.</p>
            </div>
          </div>
        </div>
      );
    default:
      return renderMainProfile();
  }
}
