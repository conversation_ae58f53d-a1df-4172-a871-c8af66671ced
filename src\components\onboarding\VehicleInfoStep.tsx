import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Car, Calendar, Palette, Hash, Building2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface VehicleInfoStepProps {
  data: any;
  onUpdate: (data: any) => void;
  onNext: () => void;
}

const vehicleMakes = [
  "Toyota", "Honda", "Nissan", "Hyundai", "Kia", "Suzuki", "Volkswagen",
  "Peugeot", "Renault", "Ford", "Chevrolet", "Mazda", "Mercedes-Benz", "BMW", "Autre"
];

const vtcCompanies = [
  "Yango", "Heetch", "InDrive", "Uber", "Bolt (ex-Taxify)", "Gozem",
  "Babi", "Indépendant", "Autre"
];

const vehicleColors = [
  "<PERSON>", "<PERSON>ir", "Gris", "<PERSON>rgent", "Ble<PERSON>", "<PERSON>", 
  "Vert", "Jaune", "Orange", "Marron", "Autre"
];

export default function VehicleInfoStep({ data, onUpdate, onNext }: VehicleInfoStepProps) {
  const [formData, setFormData] = useState({
    vehicleMake: data.vehicleMake || "",
    vehicleModel: data.vehicleModel || "",
    vehicleYear: data.vehicleYear || new Date().getFullYear(),
    vehicleColor: data.vehicleColor || "",
    licensePlate: data.licensePlate || "",
    vtcCompany: data.vtcCompany || "",
  });
  const { toast } = useToast();

  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const validateForm = () => {
    if (!formData.vehicleMake || !formData.vehicleModel) {
      toast({
        title: "Erreur",
        description: "Veuillez renseigner la marque et le modèle du véhicule.",
        variant: "destructive",
      });
      return false;
    }

    if (formData.vehicleYear < 2000 || formData.vehicleYear > new Date().getFullYear() + 1) {
      toast({
        title: "Erreur",
        description: "Veuillez renseigner une année valide.",
        variant: "destructive",
      });
      return false;
    }

    if (!formData.vehicleColor) {
      toast({
        title: "Erreur",
        description: "Veuillez sélectionner la couleur du véhicule.",
        variant: "destructive",
      });
      return false;
    }

    if (!formData.licensePlate || formData.licensePlate.length < 4) {
      toast({
        title: "Erreur",
        description: "Veuillez renseigner un numéro de plaque valide.",
        variant: "destructive",
      });
      return false;
    }

    if (!formData.vtcCompany) {
      toast({
        title: "Erreur",
        description: "Veuillez sélectionner votre compagnie VTC.",
        variant: "destructive",
      });
      return false;
    }

    return true;
  };

  const handleNext = () => {
    if (validateForm()) {
      onUpdate(formData);
      onNext();
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
          <Car size={32} className="text-primary" />
        </div>
        <h2 className="text-2xl font-bold mb-2">Informations du véhicule</h2>
        <p className="text-muted-foreground">
          Renseignez les détails de votre véhicule pour commencer à conduire avec ChauffeurX
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="vehicleMake">Marque du véhicule</Label>
          <Select
            value={formData.vehicleMake}
            onValueChange={(value) => handleInputChange("vehicleMake", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Sélectionnez la marque" />
            </SelectTrigger>
            <SelectContent>
              {vehicleMakes.map((make) => (
                <SelectItem key={make} value={make}>
                  {make}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="vehicleModel">Modèle</Label>
          <Input
            id="vehicleModel"
            placeholder="ex: Corolla, Civic, Yaris..."
            value={formData.vehicleModel}
            onChange={(e) => handleInputChange("vehicleModel", e.target.value)}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="vehicleYear">Année</Label>
          <div className="relative">
            <Calendar className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              id="vehicleYear"
              type="number"
              min="2000"
              max={new Date().getFullYear() + 1}
              placeholder="2020"
              value={formData.vehicleYear}
              onChange={(e) => handleInputChange("vehicleYear", parseInt(e.target.value))}
              className="pl-10"
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="vehicleColor">Couleur</Label>
          <Select
            value={formData.vehicleColor}
            onValueChange={(value) => handleInputChange("vehicleColor", value)}
          >
            <SelectTrigger>
              <div className="flex items-center">
                <Palette className="h-4 w-4 mr-2 text-muted-foreground" />
                <SelectValue placeholder="Sélectionnez la couleur" />
              </div>
            </SelectTrigger>
            <SelectContent>
              {vehicleColors.map((color) => (
                <SelectItem key={color} value={color}>
                  {color}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="licensePlate">Numéro de plaque d'immatriculation</Label>
        <div className="relative">
          <Hash className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            id="licensePlate"
            placeholder="ex: AB 1234 CD"
            value={formData.licensePlate}
            onChange={(e) => handleInputChange("licensePlate", e.target.value.toUpperCase())}
            className="pl-10"
          />
        </div>
        <p className="text-xs text-muted-foreground">
          Format ivoirien: AB 1234 CD
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="vtcCompany">Compagnie VTC</Label>
        <div className="relative">
          <Building2 className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Select value={formData.vtcCompany} onValueChange={(value) => handleInputChange("vtcCompany", value)}>
            <SelectTrigger className="pl-10">
              <SelectValue placeholder="Sélectionnez votre compagnie" />
            </SelectTrigger>
            <SelectContent>
              {vtcCompanies.map((company) => (
                <SelectItem key={company} value={company}>
                  {company}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <p className="text-xs text-muted-foreground">
          Choisissez la plateforme VTC avec laquelle vous travaillez
        </p>
      </div>

      <div className="bg-muted/50 p-4 rounded-lg">
        <h3 className="font-semibold mb-2">Récapitulatif</h3>
        <div className="text-sm space-y-1">
          <p><span className="font-medium">Véhicule:</span> {formData.vehicleMake} {formData.vehicleModel} ({formData.vehicleYear})</p>
          <p><span className="font-medium">Couleur:</span> {formData.vehicleColor}</p>
          <p><span className="font-medium">Plaque:</span> {formData.licensePlate}</p>
          <p><span className="font-medium">Compagnie:</span> {formData.vtcCompany}</p>
        </div>
      </div>

      <Button onClick={handleNext} className="w-full" size="lg">
        Continuer
      </Button>
    </div>
  );
}
