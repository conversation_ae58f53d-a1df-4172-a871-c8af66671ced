import { useState } from "react";
import { TrendingUp, DollarSign, Clock, Target, Calendar } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export default function EarningsScreen() {
  const [selectedPeriod, setSelectedPeriod] = useState('today');
  
  const earnings = {
    today: { amount: 45000, rides: 12, hours: 8.5 },
    week: { amount: 280000, rides: 78, hours: 52 },
    month: { amount: 1200000, rides: 340, hours: 220 }
  };

  const currentEarnings = earnings[selectedPeriod as keyof typeof earnings];

  const periods = [
    { id: 'today', label: 'Aujourd\'hui' },
    { id: 'week', label: '<PERSON><PERSON> semaine' },
    { id: 'month', label: 'Ce mois' }
  ];

  // Données simulées pour le graphique
  const weeklyData = [
    { day: 'Lun', amount: 38000 },
    { day: 'Mar', amount: 42000 },
    { day: 'Mer', amount: 35000 },
    { day: 'Jeu', amount: 48000 },
    { day: 'Ven', amount: 55000 },
    { day: 'Sam', amount: 62000 },
    { day: 'Dim', amount: 45000 }
  ];

  const maxAmount = Math.max(...weeklyData.map(d => d.amount));

  return (
    <div className="flex flex-col h-screen bg-background">
      {/* Header */}
      <div className="p-4 bg-card shadow-card">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-secondary rounded-full flex items-center justify-center">
            <DollarSign size={20} className="text-secondary-foreground" />
          </div>
          <div>
            <h1 className="text-lg font-bold">Revenus & Stats</h1>
            <p className="text-sm text-muted-foreground">Suivi de vos performances</p>
          </div>
        </div>
      </div>

      <div className="flex-1 p-4 space-y-4 overflow-y-auto pb-20">
        
        {/* Period selector */}
        <div className="flex space-x-2">
          {periods.map((period) => (
            <Button
              key={period.id}
              variant={selectedPeriod === period.id ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedPeriod(period.id)}
              className="flex-1"
            >
              {period.label}
            </Button>
          ))}
        </div>

        {/* Main earnings card */}
        <Card className="p-6 bg-gradient-primary text-primary-foreground animate-fade-in-up">
          <div className="text-center">
            <div className="text-sm opacity-90 mb-2">
              {periods.find(p => p.id === selectedPeriod)?.label}
            </div>
            <div className="text-3xl font-bold mb-1">
              {currentEarnings.amount.toLocaleString()} CFA
            </div>
            <div className="flex justify-center items-center space-x-4 text-sm opacity-90">
              <span>{currentEarnings.rides} courses</span>
              <span>•</span>
              <span>{currentEarnings.hours}h travaillées</span>
            </div>
          </div>
        </Card>

        {/* Stats cards */}
        <div className="grid grid-cols-2 gap-4">
          <Card className="p-4 animate-fade-in-up" style={{ animationDelay: '0.1s' }}>
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-success/10 rounded-full flex items-center justify-center">
                <TrendingUp size={20} className="text-success" />
              </div>
              <div>
                <div className="text-sm text-muted-foreground">Taux horaire</div>
                <div className="font-semibold">
                  {Math.round(currentEarnings.amount / currentEarnings.hours).toLocaleString()} CFA/h
                </div>
              </div>
            </div>
          </Card>

          <Card className="p-4 animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-warning/10 rounded-full flex items-center justify-center">
                <Target size={20} className="text-warning" />
              </div>
              <div>
                <div className="text-sm text-muted-foreground">Course moy.</div>
                <div className="font-semibold">
                  {Math.round(currentEarnings.amount / currentEarnings.rides).toLocaleString()} CFA
                </div>
              </div>
            </div>
          </Card>
        </div>

        {/* Weekly chart */}
        <Card className="p-4 animate-fade-in-up" style={{ animationDelay: '0.3s' }}>
          <div className="flex items-center space-x-2 mb-4">
            <Calendar size={16} className="text-primary" />
            <h3 className="font-semibold">Revenus de la semaine</h3>
          </div>
          
          <div className="space-y-3">
            {weeklyData.map((day, index) => (
              <div key={day.day} className="flex items-center space-x-3">
                <div className="w-8 text-xs text-muted-foreground font-medium">
                  {day.day}
                </div>
                <div className="flex-1 bg-muted/30 rounded-full h-2 overflow-hidden">
                  <div 
                    className="h-full bg-gradient-primary rounded-full transition-all duration-500"
                    style={{ 
                      width: `${(day.amount / maxAmount) * 100}%`,
                      animationDelay: `${index * 0.1}s`
                    }}
                  />
                </div>
                <div className="text-xs font-medium min-w-[60px] text-right">
                  {(day.amount / 1000).toFixed(0)}k
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* AI Tip */}
        <Card className="p-4 bg-secondary/5 border-secondary/20 animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
          <div className="flex items-center space-x-2 mb-2">
            <span className="text-xs font-medium text-secondary">🎯 CONSEIL IA</span>
          </div>
          <p className="text-sm">
            Votre meilleur jour est le samedi (+24% vs moyenne). 
            Essayez de travailler plus longtemps ce jour-là pour maximiser vos revenus !
          </p>
        </Card>

        {/* Time analysis */}
        <Card className="p-4 animate-fade-in-up" style={{ animationDelay: '0.5s' }}>
          <h3 className="font-semibold mb-3 flex items-center space-x-2">
            <Clock size={16} className="text-primary" />
            <span>Analyse du temps</span>
          </h3>
          
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <div className="text-muted-foreground">Temps en course</div>
              <div className="font-medium text-success">
                {Math.round((currentEarnings.hours * 0.7) * 10) / 10}h (70%)
              </div>
            </div>
            <div>
              <div className="text-muted-foreground">Temps d'attente</div>
              <div className="font-medium text-warning">
                {Math.round((currentEarnings.hours * 0.3) * 10) / 10}h (30%)
              </div>
            </div>
          </div>
        </Card>

      </div>
    </div>
  );
}