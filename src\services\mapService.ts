interface Coordinates {
  lat: number;
  lng: number;
}

interface HotZone {
  id: number;
  name: string;
  coordinates: Coordinates;
  intensity: 'Très chaud' | 'Chaud' | 'Modéré';
  demandLevel: number; // 1-100
  estimatedWaitTime: number; // minutes
  averageRideValue: number; // CFA
}

interface MapBounds {
  north: number;
  south: number;
  east: number;
  west: number;
}

interface OSMNode {
  id: string;
  lat: number;
  lon: number;
  tags?: { [key: string]: string };
}

interface OSMWay {
  id: string;
  nodes: string[];
  tags?: { [key: string]: string };
}

interface OSMResponse {
  elements: (OSMNode | OSMWay)[];
}

class MapService {
  private abidjanCenter: Coordinates = { lat: 5.3600, lng: -4.0083 };
  private abidjanBounds: MapBounds = {
    north: 5.4500,
    south: 5.2700,
    east: -3.9000,
    west: -4.1200
  };

  // Major zones in Abidjan with their approximate coordinates
  private abidjanZones: HotZone[] = [
    {
      id: 1,
      name: "<PERSON>",
      coordinates: { lat: 5.3197, lng: -4.0267 },
      intensity: "Très chaud",
      demandLevel: 85,
      estimatedWaitTime: 3,
      averageRideValue: 4500
    },
    {
      id: 2,
      name: "Adjamé",
      coordinates: { lat: 5.3667, lng: -4.0333 },
      intensity: "Chaud",
      demandLevel: 72,
      estimatedWaitTime: 5,
      averageRideValue: 3800
    },
    {
      id: 3,
      name: "Cocody",
      coordinates: { lat: 5.3500, lng: -3.9833 },
      intensity: "Modéré",
      demandLevel: 65,
      estimatedWaitTime: 8,
      averageRideValue: 5200
    },
    {
      id: 4,
      name: "Zone 4 Marcory",
      coordinates: { lat: 5.2833, lng: -4.0167 },
      intensity: "Chaud",
      demandLevel: 78,
      estimatedWaitTime: 4,
      averageRideValue: 4200
    },
    {
      id: 5,
      name: "Treichville",
      coordinates: { lat: 5.3000, lng: -4.0333 },
      intensity: "Modéré",
      demandLevel: 58,
      estimatedWaitTime: 10,
      averageRideValue: 3500
    },
    {
      id: 6,
      name: "Yopougon",
      coordinates: { lat: 5.3333, lng: -4.0833 },
      intensity: "Modéré",
      demandLevel: 62,
      estimatedWaitTime: 7,
      averageRideValue: 3200
    },
    {
      id: 7,
      name: "Riviera",
      coordinates: { lat: 5.3833, lng: -3.9667 },
      intensity: "Chaud",
      demandLevel: 75,
      estimatedWaitTime: 6,
      averageRideValue: 5800
    }
  ];

  async getMapData(): Promise<OSMResponse> {
    try {
      const overpassQuery = `
        [out:json][timeout:25];
        (
          way["highway"~"^(primary|secondary|tertiary|trunk|residential)$"](${this.abidjanBounds.south},${this.abidjanBounds.west},${this.abidjanBounds.north},${this.abidjanBounds.east});
          way["amenity"~"^(hospital|school|university|shopping|restaurant)$"](${this.abidjanBounds.south},${this.abidjanBounds.west},${this.abidjanBounds.north},${this.abidjanBounds.east});
          node["place"~"^(suburb|neighbourhood)$"](${this.abidjanBounds.south},${this.abidjanBounds.west},${this.abidjanBounds.north},${this.abidjanBounds.east});
        );
        out geom;
      `;

      const response = await fetch('https://overpass-api.de/api/interpreter', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `data=${encodeURIComponent(overpassQuery)}`
      });

      if (!response.ok) {
        throw new Error(`OpenStreetMap API error: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching map data:', error);
      // Return minimal fallback data
      return {
        elements: []
      };
    }
  }

  getHotZones(): HotZone[] {
    // Simulate real-time demand fluctuations
    return this.abidjanZones.map(zone => ({
      ...zone,
      demandLevel: this.simulateRealTimeDemand(zone.demandLevel),
      estimatedWaitTime: this.simulateWaitTime(zone.estimatedWaitTime),
      intensity: this.calculateIntensity(zone.demandLevel)
    }));
  }

  getCurrentLocation(): Promise<Coordinates> {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation is not supported by this browser'));
        return;
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          resolve({
            lat: position.coords.latitude,
            lng: position.coords.longitude
          });
        },
        (error) => {
          console.error('Error getting location:', error);
          // Fallback to Abidjan center
          resolve(this.abidjanCenter);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000 // 5 minutes
        }
      );
    });
  }

  calculateDistance(point1: Coordinates, point2: Coordinates): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(point2.lat - point1.lat);
    const dLng = this.toRadians(point2.lng - point1.lng);
    
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(this.toRadians(point1.lat)) * Math.cos(this.toRadians(point2.lat)) *
              Math.sin(dLng / 2) * Math.sin(dLng / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  findNearestHotZone(currentLocation: Coordinates): HotZone | null {
    const hotZones = this.getHotZones();
    let nearest: HotZone | null = null;
    let minDistance = Infinity;

    hotZones.forEach(zone => {
      const distance = this.calculateDistance(currentLocation, zone.coordinates);
      if (distance < minDistance) {
        minDistance = distance;
        nearest = zone;
      }
    });

    return nearest;
  }

  getZoneByName(zoneName: string): HotZone | null {
    return this.abidjanZones.find(zone => 
      zone.name.toLowerCase().includes(zoneName.toLowerCase())
    ) || null;
  }

  getAbidjanBounds(): MapBounds {
    return this.abidjanBounds;
  }

  getAbidjanCenter(): Coordinates {
    return this.abidjanCenter;
  }

  private simulateRealTimeDemand(baseDemand: number): number {
    // Add some randomness to simulate real-time fluctuations
    const variation = (Math.random() - 0.5) * 20; // ±10 points
    const timeOfDayFactor = this.getTimeOfDayFactor();
    return Math.max(10, Math.min(100, Math.round(baseDemand + variation + timeOfDayFactor)));
  }

  private simulateWaitTime(baseWaitTime: number): number {
    // Simulate wait time variations
    const variation = (Math.random() - 0.5) * 4; // ±2 minutes
    return Math.max(1, Math.round(baseWaitTime + variation));
  }

  private calculateIntensity(demandLevel: number): 'Très chaud' | 'Chaud' | 'Modéré' {
    if (demandLevel >= 80) return 'Très chaud';
    if (demandLevel >= 65) return 'Chaud';
    return 'Modéré';
  }

  private getTimeOfDayFactor(): number {
    const hour = new Date().getHours();
    
    // Peak hours: 7-9 AM and 5-7 PM
    if ((hour >= 7 && hour <= 9) || (hour >= 17 && hour <= 19)) {
      return 15; // +15 demand during peak hours
    }
    
    // Late night: 10 PM - 6 AM
    if (hour >= 22 || hour <= 6) {
      return -10; // -10 demand during late night
    }
    
    // Regular hours
    return 0;
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }
}

export const mapService = new MapService();
export type { Coordinates, HotZone, MapBounds, OSMResponse };
