import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card } from "@/components/ui/card";
import { Upload, FileText, Calendar, CheckCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface LicenseVerificationStepProps {
  data: any;
  onUpdate: (data: any) => void;
  onNext: () => void;
}

export default function LicenseVerificationStep({ data, onUpdate, onNext }: LicenseVerificationStepProps) {
  const [formData, setFormData] = useState({
    licenseNumber: data.licenseNumber || "",
    licenseExpiry: data.licenseExpiry || "",
    licensePhoto: data.licensePhoto || null,
  });
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'uploading' | 'success'>('idle');
  const { toast } = useToast();

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadStatus('uploading');
      // Simulate upload
      setTimeout(() => {
        setFormData(prev => ({ ...prev, licensePhoto: file }));
        setUploadStatus('success');
        toast({
          title: "Photo téléchargée",
          description: "Votre permis de conduire a été téléchargé avec succès.",
        });
      }, 1500);
    }
  };

  const validateForm = () => {
    if (!formData.licenseNumber || formData.licenseNumber.length < 8) {
      toast({
        title: "Erreur",
        description: "Veuillez renseigner un numéro de permis valide.",
        variant: "destructive",
      });
      return false;
    }

    if (!formData.licenseExpiry) {
      toast({
        title: "Erreur",
        description: "Veuillez renseigner la date d'expiration du permis.",
        variant: "destructive",
      });
      return false;
    }

    const expiryDate = new Date(formData.licenseExpiry);
    if (expiryDate <= new Date()) {
      toast({
        title: "Erreur",
        description: "Votre permis de conduire est expiré.",
        variant: "destructive",
      });
      return false;
    }

    return true;
  };

  const handleNext = () => {
    if (validateForm()) {
      onUpdate(formData);
      onNext();
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
          <FileText size={32} className="text-primary" />
        </div>
        <h2 className="text-2xl font-bold mb-2">Vérification du permis</h2>
        <p className="text-muted-foreground">
          Téléchargez votre permis de conduire pour vérifier votre éligibilité
        </p>
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="licenseNumber">Numéro de permis de conduire</Label>
          <Input
            id="licenseNumber"
            placeholder="ex: CI123456789"
            value={formData.licenseNumber}
            onChange={(e) => handleInputChange("licenseNumber", e.target.value.toUpperCase())}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="licenseExpiry">Date d'expiration</Label>
          <div className="relative">
            <Calendar className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              id="licenseExpiry"
              type="date"
              value={formData.licenseExpiry}
              onChange={(e) => handleInputChange("licenseExpiry", e.target.value)}
              className="pl-10"
              min={new Date().toISOString().split('T')[0]}
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label>Photo du permis de conduire</Label>
          <Card className="p-6 border-dashed border-2 border-muted-foreground/25">
            <div className="text-center">
              {uploadStatus === 'success' ? (
                <div className="space-y-2">
                  <CheckCircle className="w-12 h-12 text-green-500 mx-auto" />
                  <p className="text-sm font-medium">Permis téléchargé avec succès</p>
                  <p className="text-xs text-muted-foreground">
                    {formData.licensePhoto?.name}
                  </p>
                </div>
              ) : uploadStatus === 'uploading' ? (
                <div className="space-y-2">
                  <div className="w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto"></div>
                  <p className="text-sm">Téléchargement en cours...</p>
                </div>
              ) : (
                <div className="space-y-2">
                  <Upload className="w-12 h-12 text-muted-foreground mx-auto" />
                  <div>
                    <p className="text-sm font-medium">Cliquez pour télécharger</p>
                    <p className="text-xs text-muted-foreground">
                      PNG, JPG jusqu'à 5MB
                    </p>
                  </div>
                </div>
              )}
              <input
                type="file"
                accept="image/*"
                onChange={handleFileUpload}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              />
            </div>
          </Card>
        </div>
      </div>

      <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
        <h3 className="font-semibold text-blue-900 mb-2">Informations importantes</h3>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Votre permis doit être valide et en cours de validité</li>
          <li>• La photo doit être claire et lisible</li>
          <li>• Vos informations seront vérifiées sous 24h</li>
        </ul>
      </div>

      <Button onClick={handleNext} className="w-full" size="lg">
        Continuer
      </Button>
    </div>
  );
}
