import { useState, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card } from "@/components/ui/card";
import { Upload, FileText, Calendar, CheckCircle, Camera, Image } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface LicenseVerificationStepProps {
  data: any;
  onUpdate: (data: any) => void;
  onNext: () => void;
}

export default function LicenseVerificationStep({ data, onUpdate, onNext }: LicenseVerificationStepProps) {
  const [formData, setFormData] = useState({
    licenseNumber: data.licenseNumber || "",
    licenseExpiry: data.licenseExpiry || "",
    licensePhoto: data.licensePhoto || null,
  });
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'uploading' | 'success'>('idle');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: "Erreur",
          description: "Le fichier est trop volumineux. Taille maximale : 5MB.",
          variant: "destructive",
        });
        return;
      }

      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast({
          title: "Erreur",
          description: "Veuillez sélectionner un fichier image (PNG, JPG, JPEG).",
          variant: "destructive",
        });
        return;
      }

      setUploadStatus('uploading');
      // Simulate upload
      setTimeout(() => {
        setFormData(prev => ({ ...prev, licensePhoto: file }));
        setUploadStatus('success');
        toast({
          title: "Photo téléchargée",
          description: "Votre permis de conduire a été téléchargé avec succès.",
        });
      }, 1500);
    }
  };

  const handleTakePhoto = () => {
    if (fileInputRef.current) {
      fileInputRef.current.setAttribute('capture', 'environment');
      fileInputRef.current.click();
    }
  };

  const handleChooseFile = () => {
    if (fileInputRef.current) {
      fileInputRef.current.removeAttribute('capture');
      fileInputRef.current.click();
    }
  };

  const handleRetakePhoto = () => {
    setFormData(prev => ({ ...prev, licensePhoto: null }));
    setUploadStatus('idle');
  };

  const validateForm = () => {
    if (!formData.licenseNumber || formData.licenseNumber.length < 8) {
      toast({
        title: "Erreur",
        description: "Veuillez renseigner un numéro de permis valide.",
        variant: "destructive",
      });
      return false;
    }

    if (!formData.licenseExpiry) {
      toast({
        title: "Erreur",
        description: "Veuillez renseigner la date d'expiration du permis.",
        variant: "destructive",
      });
      return false;
    }

    const expiryDate = new Date(formData.licenseExpiry);
    if (expiryDate <= new Date()) {
      toast({
        title: "Erreur",
        description: "Votre permis de conduire est expiré.",
        variant: "destructive",
      });
      return false;
    }

    return true;
  };

  const handleNext = () => {
    if (validateForm()) {
      onUpdate(formData);
      onNext();
    }
  };

  return (
    <div className="space-y-6 px-6 py-4">
      <div className="text-center">
        <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg">
          <FileText size={36} className="text-white" />
        </div>
        <h2 className="text-uber-title text-gray-900 mb-3">Vérification du permis</h2>
        <p className="text-uber-body text-gray-600 max-w-sm mx-auto">
          Ajoutez une photo de votre permis de conduire pour vérifier votre éligibilité
        </p>
      </div>

      <div className="space-y-6">
        <div className="space-y-3">
          <Label htmlFor="licenseNumber" className="text-uber-body font-medium text-gray-900">
            Numéro de permis de conduire
          </Label>
          <Input
            id="licenseNumber"
            placeholder="ex: CI123456789"
            value={formData.licenseNumber}
            onChange={(e) => handleInputChange("licenseNumber", e.target.value.toUpperCase())}
            className="input-uber"
          />
        </div>

        <div className="space-y-3">
          <Label htmlFor="licenseExpiry" className="text-uber-body font-medium text-gray-900">
            Date d'expiration
          </Label>
          <div className="relative">
            <Calendar className="absolute left-5 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <Input
              id="licenseExpiry"
              type="date"
              value={formData.licenseExpiry}
              onChange={(e) => handleInputChange("licenseExpiry", e.target.value)}
              className="input-uber pl-14"
              min={new Date().toISOString().split('T')[0]}
            />
          </div>
        </div>

        <div className="space-y-3">
          <Label className="text-uber-body font-medium text-gray-900">Photo du permis de conduire</Label>
          <Card className="p-6 border-dashed border-2 border-muted-foreground/25">
            <div className="text-center">
              {uploadStatus === 'success' ? (
                <div className="space-y-4">
                  <CheckCircle className="w-12 h-12 text-green-500 mx-auto" />
                  <p className="text-sm font-medium">Permis téléchargé avec succès</p>
                  <p className="text-xs text-muted-foreground">
                    {formData.licensePhoto?.name}
                  </p>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={handleRetakePhoto}
                    className="mt-2"
                  >
                    Changer la photo
                  </Button>
                </div>
              ) : uploadStatus === 'uploading' ? (
                <div className="space-y-2">
                  <div className="w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto"></div>
                  <p className="text-sm">Téléchargement en cours...</p>
                </div>
              ) : (
                <div className="space-y-4">
                  <Upload className="w-12 h-12 text-muted-foreground mx-auto" />
                  <div>
                    <p className="text-sm font-medium">Ajoutez une photo de votre permis</p>
                    <p className="text-xs text-muted-foreground">
                      PNG, JPG jusqu'à 5MB
                    </p>
                  </div>

                  {/* Mobile-optimized action buttons */}
                  <div className="flex flex-col space-y-3 mt-4">
                    <Button
                      type="button"
                      onClick={handleTakePhoto}
                      className="w-full h-12 bg-blue-600 hover:bg-blue-700 text-white rounded-xl font-medium flex items-center justify-center space-x-2"
                    >
                      <Camera size={20} />
                      <span>Prendre une photo</span>
                    </Button>

                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleChooseFile}
                      className="w-full h-12 border-2 border-gray-300 hover:border-gray-400 rounded-xl font-medium flex items-center justify-center space-x-2"
                    >
                      <Image size={20} />
                      <span>Choisir depuis la galerie</span>
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </Card>

          {/* Hidden file input - only triggered by explicit button clicks */}
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileUpload}
            className="hidden"
            aria-hidden="true"
          />
        </div>
      </div>

      <div className="card-uber p-5 bg-blue-50 border-l-4 border-blue-500">
        <h3 className="text-uber-subtitle text-blue-900 mb-3">Informations importantes</h3>
        <ul className="text-uber-body text-blue-800 space-y-2">
          <li className="flex items-start space-x-2">
            <span className="text-blue-600 mt-1">•</span>
            <span>Votre permis doit être valide et en cours de validité</span>
          </li>
          <li className="flex items-start space-x-2">
            <span className="text-blue-600 mt-1">•</span>
            <span>La photo doit être claire et lisible</span>
          </li>
          <li className="flex items-start space-x-2">
            <span className="text-blue-600 mt-1">•</span>
            <span>Vos informations seront vérifiées sous 24h</span>
          </li>
        </ul>
      </div>

      <Button
        onClick={handleNext}
        className="w-full h-14 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-2xl font-semibold text-lg shadow-lg transition-all duration-200 transform hover:scale-105 active:scale-95"
      >
        Continuer
      </Button>
    </div>
  );
}
