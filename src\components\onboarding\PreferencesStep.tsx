import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { Settings, MapPin, Bell, Clock, TrendingUp } from "lucide-react";

interface PreferencesStepProps {
  data: any;
  onUpdate: (data: any) => void;
  onNext: () => void;
}

const abidjanZones = [
  "Plateau", "Adjamé", "Cocody", "Zone 4", "Marcory", 
  "Treichville", "Yopougon", "Riviera", "Abobo", "Port-Bouët"
];

export default function PreferencesStep({ data, onUpdate, onNext }: PreferencesStepProps) {
  const [preferences, setPreferences] = useState({
    preferredZones: data.preferredZones || [],
    workingHours: data.workingHours || { start: "07:00", end: "19:00" },
    notifications: data.notifications || {
      hotZones: true,
      weather: true,
      earnings: true
    }
  });

  const toggleZone = (zone: string) => {
    setPreferences(prev => ({
      ...prev,
      preferredZones: prev.preferredZones.includes(zone)
        ? prev.preferredZones.filter(z => z !== zone)
        : [...prev.preferredZones, zone]
    }));
  };

  const updateNotification = (key: string, value: boolean) => {
    setPreferences(prev => ({
      ...prev,
      notifications: {
        ...prev.notifications,
        [key]: value
      }
    }));
  };

  const updateWorkingHours = (key: string, value: string) => {
    setPreferences(prev => ({
      ...prev,
      workingHours: {
        ...prev.workingHours,
        [key]: value
      }
    }));
  };

  const handleNext = () => {
    onUpdate(preferences);
    onNext();
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
          <Settings size={32} className="text-primary" />
        </div>
        <h2 className="text-2xl font-bold mb-2">Préférences de travail</h2>
        <p className="text-muted-foreground">
          Configurez vos préférences pour optimiser votre expérience
        </p>
      </div>

      <Card className="p-4">
        <div className="flex items-center space-x-2 mb-4">
          <MapPin className="w-5 h-5 text-primary" />
          <h3 className="font-semibold">Zones préférées</h3>
        </div>
        <p className="text-sm text-muted-foreground mb-4">
          Sélectionnez les zones où vous préférez travailler (3-5 recommandées)
        </p>
        <div className="flex flex-wrap gap-2">
          {abidjanZones.map((zone) => (
            <Badge
              key={zone}
              variant={preferences.preferredZones.includes(zone) ? "default" : "outline"}
              className="cursor-pointer"
              onClick={() => toggleZone(zone)}
            >
              {zone}
            </Badge>
          ))}
        </div>
      </Card>

      <Card className="p-4">
        <div className="flex items-center space-x-2 mb-4">
          <Clock className="w-5 h-5 text-primary" />
          <h3 className="font-semibold">Heures de travail préférées</h3>
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="startTime">Début</Label>
            <input
              id="startTime"
              type="time"
              value={preferences.workingHours.start}
              onChange={(e) => updateWorkingHours("start", e.target.value)}
              className="w-full px-3 py-2 border border-input rounded-md"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="endTime">Fin</Label>
            <input
              id="endTime"
              type="time"
              value={preferences.workingHours.end}
              onChange={(e) => updateWorkingHours("end", e.target.value)}
              className="w-full px-3 py-2 border border-input rounded-md"
            />
          </div>
        </div>
      </Card>

      <Card className="p-4">
        <div className="flex items-center space-x-2 mb-4">
          <Bell className="w-5 h-5 text-primary" />
          <h3 className="font-semibold">Notifications</h3>
        </div>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="hotZones">Alertes zones chaudes</Label>
              <p className="text-xs text-muted-foreground">
                Recevoir des notifications pour les zones à forte demande
              </p>
            </div>
            <Switch
              id="hotZones"
              checked={preferences.notifications.hotZones}
              onCheckedChange={(checked) => updateNotification("hotZones", checked)}
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="weather">Alertes météo</Label>
              <p className="text-xs text-muted-foreground">
                Notifications sur l'impact météo sur la demande
              </p>
            </div>
            <Switch
              id="weather"
              checked={preferences.notifications.weather}
              onCheckedChange={(checked) => updateNotification("weather", checked)}
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="earnings">Rappels revenus</Label>
              <p className="text-xs text-muted-foreground">
                Résumés quotidiens et conseils pour optimiser les gains
              </p>
            </div>
            <Switch
              id="earnings"
              checked={preferences.notifications.earnings}
              onCheckedChange={(checked) => updateNotification("earnings", checked)}
            />
          </div>
        </div>
      </Card>

      <div className="bg-green-50 p-4 rounded-lg border border-green-200">
        <div className="flex items-center space-x-2 mb-2">
          <TrendingUp className="w-5 h-5 text-green-600" />
          <h3 className="font-semibold text-green-900">Récapitulatif</h3>
        </div>
        <div className="text-sm text-green-800 space-y-1">
          <p><span className="font-medium">Zones préférées:</span> {preferences.preferredZones.length} sélectionnées</p>
          <p><span className="font-medium">Heures de travail:</span> {preferences.workingHours.start} - {preferences.workingHours.end}</p>
          <p><span className="font-medium">Notifications:</span> {Object.values(preferences.notifications).filter(Boolean).length}/3 activées</p>
        </div>
      </div>

      <Button onClick={handleNext} className="w-full" size="lg">
        Continuer vers le tutoriel
      </Button>
    </div>
  );
}
