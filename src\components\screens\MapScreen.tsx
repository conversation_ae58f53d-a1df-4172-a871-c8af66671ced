import { useState, useEffect } from "react";
import { Navigation, MapPin, Cloud, Flame, Zap, Loader2, RefreshCw } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { weatherService, type WeatherData } from "@/services/weatherService";
import { mapService, type HotZone, type Coordinates } from "@/services/mapService";
import { useToast } from "@/hooks/use-toast";
import { lazy, Suspense } from 'react';
import SimpleMap from '@/components/SimpleMap';
import FallbackMap from '@/components/FallbackMap';

// Dynamically import the map component to avoid SSR issues
const InteractiveMap = lazy(() => import('@/components/InteractiveMap'));

export default function MapScreen() {
  const [weather, setWeather] = useState<WeatherData | null>(null);
  const [hotZones, setHotZones] = useState<HotZone[]>([]);
  const [currentLocation, setCurrentLocation] = useState<Coordinates | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [time] = useState(new Date().toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' }));
  const { toast } = useToast();

  const fetchData = async () => {
    try {
      setLoading(true);
      const [weatherData, location, zones] = await Promise.all([
        weatherService.getCurrentWeather(),
        mapService.getCurrentLocation(),
        Promise.resolve(mapService.getHotZones())
      ]);

      setWeather(weatherData);
      setCurrentLocation(location);
      setHotZones(zones);
    } catch (error) {
      console.error('Error fetching map data:', error);
      toast({
        title: "Erreur",
        description: "Impossible de charger les données de la carte.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const refreshData = async () => {
    try {
      setRefreshing(true);
      const [weatherData, zones] = await Promise.all([
        weatherService.getCurrentWeather(),
        Promise.resolve(mapService.getHotZones())
      ]);

      setWeather(weatherData);
      setHotZones(zones);

      toast({
        title: "Données mises à jour",
        description: "Carte et météo actualisées avec succès.",
      });
    } catch (error) {
      console.error('Error refreshing data:', error);
      toast({
        title: "Erreur",
        description: "Impossible de rafraîchir les données.",
        variant: "destructive",
      });
    } finally {
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchData();
    // Refresh data every 5 minutes
    const interval = setInterval(refreshData, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  const handleZoneClick = (zone: HotZone) => {
    toast({
      title: zone.name,
      description: `Demande: ${zone.demandLevel}% • Attente: ${zone.estimatedWaitTime}min • Revenu moyen: ${zone.averageRideValue} CFA`,
    });
  };

  const handleNavigateToZone = (zone: HotZone) => {
    toast({
      title: "Navigation vers zone chaude",
      description: `Direction ${zone.name} - Demande: ${zone.demandLevel}%`,
    });
  };

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      {/* Uber-style Header */}
      <div className="bg-white shadow-sm border-b border-gray-100 safe-area-top">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-uber-title text-gray-900">Zones Chaudes</h1>
              <div className="flex items-center mt-1">
                {loading ? (
                  <Loader2 size={16} className="animate-spin text-gray-400 mr-2" />
                ) : (
                  <Cloud size={16} className="text-blue-500 mr-2" />
                )}
                <p className="text-uber-caption text-gray-600">
                  {loading ? "Chargement..." : weather ? `${weather.temperature}°C, ${weather.description}` : "N/A"}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="text-right">
                <div className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-semibold">
                  {hotZones.filter(zone => zone.demandLevel > 70).length} zones actives
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {time}
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={refreshData}
                disabled={refreshing}
                className="w-10 h-10 rounded-full hover:bg-gray-100"
              >
                <RefreshCw size={18} className={refreshing ? "animate-spin" : ""} />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Interactive Map */}
      <div className="flex-1 relative overflow-hidden">
        <SimpleMap
          hotZones={hotZones}
          currentLocation={currentLocation}
          onZoneClick={handleZoneClick}
          onNavigateToZone={handleNavigateToZone}
          className="w-full h-full"
        />

        {/* Uber-style Floating Action Button */}
        <div className="absolute bottom-28 right-6">
          <Button
            className="w-14 h-14 bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white rounded-full shadow-2xl border-4 border-white transform hover:scale-110 transition-all duration-300 active:scale-95"
            onClick={() => {
              const bestZone = hotZones.reduce((best, zone) =>
                zone.demandLevel > best.demandLevel ? zone : best,
                hotZones[0]
              );
              if (bestZone) {
                toast({
                  title: "Navigation vers zone chaude",
                  description: `Direction ${bestZone.name} - Demande: ${bestZone.demandLevel}%`,
                });
              }
            }}
            disabled={loading || hotZones.length === 0}
          >
            <Flame size={24} />
          </Button>
        </div>

        {/* Uber-style Info Card */}
        <div className="absolute top-6 left-6 right-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-uber-body font-medium text-gray-900">
                  {currentLocation ? "Position GPS active" : "Position non disponible"}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <Zap size={16} className="text-yellow-500" />
                <span className="text-uber-caption text-gray-600">
                  {hotZones.filter(z => z.demandLevel > 70).length} zones chaudes
                </span>
              </div>
            </div>
            {weather && (
              <div className="mt-3 pt-3 border-t border-gray-200">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">
                    {weather.description} • {weather.humidity}% humidité
                  </span>
                  <span className="text-blue-600 font-medium">
                    {weatherService.getWeatherImpactOnDemand(weather)}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}