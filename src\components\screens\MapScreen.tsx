import { useState, useEffect } from "react";
import { Navigation, MapPin, Cloud, Flame, Zap, Loader2, RefreshCw } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { weatherService, type WeatherData } from "@/services/weatherService";
import { mapService, type HotZone, type Coordinates } from "@/services/mapService";
import { useToast } from "@/hooks/use-toast";

export default function MapScreen() {
  const [weather, setWeather] = useState<WeatherData | null>(null);
  const [hotZones, setHotZones] = useState<HotZone[]>([]);
  const [currentLocation, setCurrentLocation] = useState<Coordinates | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [time] = useState(new Date().toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' }));
  const { toast } = useToast();

  const fetchData = async () => {
    try {
      setLoading(true);
      const [weatherData, location, zones] = await Promise.all([
        weatherService.getCurrentWeather(),
        mapService.getCurrentLocation(),
        Promise.resolve(mapService.getHotZones())
      ]);

      setWeather(weatherData);
      setCurrentLocation(location);
      setHotZones(zones);
    } catch (error) {
      console.error('Error fetching map data:', error);
      toast({
        title: "Erreur",
        description: "Impossible de charger les données de la carte.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const refreshData = async () => {
    try {
      setRefreshing(true);
      const [weatherData, zones] = await Promise.all([
        weatherService.getCurrentWeather(),
        Promise.resolve(mapService.getHotZones())
      ]);

      setWeather(weatherData);
      setHotZones(zones);

      toast({
        title: "Données mises à jour",
        description: "Carte et météo actualisées avec succès.",
      });
    } catch (error) {
      console.error('Error refreshing data:', error);
      toast({
        title: "Erreur",
        description: "Impossible de rafraîchir les données.",
        variant: "destructive",
      });
    } finally {
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchData();
    // Refresh data every 5 minutes
    const interval = setInterval(refreshData, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  const getZonePosition = (zone: HotZone, index: number) => {
    // Convert real coordinates to screen positions (simplified)
    const positions = [
      { top: "30%", left: "45%" }, // Plateau
      { top: "25%", left: "30%" }, // Adjamé
      { top: "40%", left: "60%" }, // Cocody
      { top: "55%", left: "40%" }, // Zone 4
      { top: "45%", left: "35%" }, // Treichville
      { top: "20%", left: "25%" }, // Yopougon
      { top: "15%", left: "65%" }, // Riviera
    ];
    return positions[index] || { top: "50%", left: "50%" };
  };

  return (
    <div className="flex flex-col h-screen bg-background">
      {/* Header avec météo et heure */}
      <div className="flex justify-between items-center p-4 bg-card shadow-card">
        <div className="flex items-center space-x-2">
          {loading ? (
            <Loader2 size={20} className="animate-spin text-muted-foreground" />
          ) : (
            <Cloud size={20} className="text-muted-foreground" />
          )}
          <span className="text-sm font-medium">
            {loading ? "Chargement..." : weather ? `${weather.temperature}°C` : "N/A"}
          </span>
        </div>
        <div className="flex items-center space-x-2">
          <h1 className="text-lg font-bold text-primary">ChauffeurX</h1>
          <Button
            variant="ghost"
            size="sm"
            onClick={refreshData}
            disabled={refreshing}
            className="p-1"
          >
            <RefreshCw size={16} className={refreshing ? "animate-spin" : ""} />
          </Button>
        </div>
        <div className="text-sm font-medium">{time}</div>
      </div>

      {/* Zone de carte */}
      <div className="flex-1 relative bg-muted/30 overflow-hidden">
        {/* Carte simulée */}
        <div className="w-full h-full bg-gradient-to-br from-muted/20 to-muted/40 relative">
          
          {/* Position du chauffeur */}
          {currentLocation && (
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
              <div className="w-4 h-4 bg-secondary rounded-full border-2 border-white shadow-button animate-bounce-gentle"></div>
              <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2">
                <span className="text-xs font-medium bg-card px-2 py-1 rounded shadow-button whitespace-nowrap">
                  Vous êtes ici
                </span>
              </div>
            </div>
          )}

          {/* Zones chaudes */}
          {hotZones.map((zone, index) => {
            const position = getZonePosition(zone, index);
            return (
              <div
                key={zone.id}
                className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer"
                style={{ top: position.top, left: position.left }}
                onClick={() => toast({
                  title: zone.name,
                  description: `Demande: ${zone.demandLevel}% • Attente: ${zone.estimatedWaitTime}min • Revenu moyen: ${zone.averageRideValue} CFA`,
                })}
              >
                <div className={`
                  w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110
                  ${zone.intensity === "Très chaud" ? "bg-hot-zone/20 border-2 border-hot-zone animate-hot-pulse" : ""}
                  ${zone.intensity === "Chaud" ? "bg-warning/20 border-2 border-warning" : ""}
                  ${zone.intensity === "Modéré" ? "bg-primary/20 border-2 border-primary" : ""}
                `}>
                  <Flame
                    size={24}
                    className={`
                      ${zone.intensity === "Très chaud" ? "text-hot-zone" : ""}
                      ${zone.intensity === "Chaud" ? "text-warning" : ""}
                      ${zone.intensity === "Modéré" ? "text-primary" : ""}
                    `}
                  />
                </div>
                <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2">
                  <div className="text-center">
                    <span className="text-xs font-medium bg-card px-2 py-1 rounded shadow-button whitespace-nowrap block">
                      {zone.name}
                    </span>
                    <span className="text-xs text-muted-foreground bg-card/80 px-1 rounded mt-1 block">
                      {zone.demandLevel}%
                    </span>
                  </div>
                </div>
              </div>
            );
          })}

          {/* Lignes de route simulées */}
          <div className="absolute inset-0 opacity-30">
            <svg className="w-full h-full">
              <path
                d="M 20 100 Q 200 50 400 150"
                stroke="currentColor"
                strokeWidth="2"
                fill="none"
                className="text-muted-foreground"
              />
              <path
                d="M 50 200 Q 300 180 380 300"
                stroke="currentColor"
                strokeWidth="2"
                fill="none"
                className="text-muted-foreground"
              />
            </svg>
          </div>
        </div>

        {/* Bouton flottant */}
        <div className="absolute bottom-24 left-1/2 transform -translate-x-1/2">
          <Button
            className="bg-gradient-primary text-primary-foreground shadow-float px-6 py-3 rounded-full font-medium"
            size="lg"
            onClick={() => {
              const bestZone = hotZones.reduce((best, zone) =>
                zone.demandLevel > best.demandLevel ? zone : best,
                hotZones[0]
              );
              if (bestZone) {
                toast({
                  title: "Navigation vers zone chaude",
                  description: `Direction ${bestZone.name} - Demande: ${bestZone.demandLevel}%`,
                });
              }
            }}
            disabled={loading || hotZones.length === 0}
          >
            <Flame className="mr-2" size={20} />
            Aller vers Coin Chaud
          </Button>
        </div>

        {/* Info card en haut */}
        <Card className="absolute top-4 left-4 right-4 p-3 bg-card/95 backdrop-blur-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <MapPin size={16} className="text-primary" />
              <span className="text-sm font-medium">
                {currentLocation ? "Position GPS active" : "Position non disponible"}
              </span>
            </div>
            <div className="flex items-center space-x-1">
              <Zap size={14} className="text-warning" />
              <span className="text-xs text-muted-foreground">
                {hotZones.filter(z => z.demandLevel > 70).length} zones chaudes
              </span>
            </div>
          </div>
          {weather && (
            <div className="mt-2 pt-2 border-t border-border/50">
              <div className="flex items-center justify-between text-xs">
                <span className="text-muted-foreground">
                  {weather.description} • {weather.humidity}% humidité
                </span>
                <span className="text-muted-foreground">
                  {weatherService.getWeatherImpactOnDemand(weather)}
                </span>
              </div>
            </div>
          )}
        </Card>
      </div>
    </div>
  );
}