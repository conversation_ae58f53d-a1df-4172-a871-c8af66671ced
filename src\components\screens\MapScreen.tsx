import { useState } from "react";
import { Navigation, MapPin, Cloud, Flame, Zap } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";

export default function MapScreen() {
  const [weather] = useState("26°C");
  const [time] = useState(new Date().toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' }));

  const hotZones = [
    { id: 1, name: "Plateau", intensity: "Très chaud", position: { top: "30%", left: "45%" } },
    { id: 2, name: "<PERSON><PERSON><PERSON>", intensity: "Chaud", position: { top: "25%", left: "30%" } },
    { id: 3, name: "Coco<PERSON>", intensity: "Modéré", position: { top: "40%", left: "60%" } },
  ];

  return (
    <div className="flex flex-col h-screen bg-background">
      {/* Header avec météo et heure */}
      <div className="flex justify-between items-center p-4 bg-card shadow-card">
        <div className="flex items-center space-x-2">
          <Cloud size={20} className="text-muted-foreground" />
          <span className="text-sm font-medium">{weather}</span>
        </div>
        <h1 className="text-lg font-bold text-primary">ChauffeurX</h1>
        <div className="text-sm font-medium">{time}</div>
      </div>

      {/* Zone de carte */}
      <div className="flex-1 relative bg-muted/30 overflow-hidden">
        {/* Carte simulée */}
        <div className="w-full h-full bg-gradient-to-br from-muted/20 to-muted/40 relative">
          
          {/* Position du chauffeur */}
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <div className="w-4 h-4 bg-secondary rounded-full border-2 border-white shadow-button animate-bounce-gentle"></div>
          </div>

          {/* Zones chaudes */}
          {hotZones.map((zone) => (
            <div
              key={zone.id}
              className="absolute transform -translate-x-1/2 -translate-y-1/2"
              style={{ top: zone.position.top, left: zone.position.left }}
            >
              <div className={`
                w-16 h-16 rounded-full flex items-center justify-center
                ${zone.intensity === "Très chaud" ? "bg-hot-zone/20 border-2 border-hot-zone animate-hot-pulse" : ""}
                ${zone.intensity === "Chaud" ? "bg-warning/20 border-2 border-warning" : ""}
                ${zone.intensity === "Modéré" ? "bg-primary/20 border-2 border-primary" : ""}
              `}>
                <Flame 
                  size={24} 
                  className={`
                    ${zone.intensity === "Très chaud" ? "text-hot-zone" : ""}
                    ${zone.intensity === "Chaud" ? "text-warning" : ""}
                    ${zone.intensity === "Modéré" ? "text-primary" : ""}
                  `}
                />
              </div>
              <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2">
                <span className="text-xs font-medium bg-card px-2 py-1 rounded shadow-button whitespace-nowrap">
                  {zone.name}
                </span>
              </div>
            </div>
          ))}

          {/* Lignes de route simulées */}
          <div className="absolute inset-0 opacity-30">
            <svg className="w-full h-full">
              <path
                d="M 20 100 Q 200 50 400 150"
                stroke="currentColor"
                strokeWidth="2"
                fill="none"
                className="text-muted-foreground"
              />
              <path
                d="M 50 200 Q 300 180 380 300"
                stroke="currentColor"
                strokeWidth="2"
                fill="none"
                className="text-muted-foreground"
              />
            </svg>
          </div>
        </div>

        {/* Bouton flottant */}
        <div className="absolute bottom-24 left-1/2 transform -translate-x-1/2">
          <Button 
            className="bg-gradient-primary text-primary-foreground shadow-float px-6 py-3 rounded-full font-medium"
            size="lg"
          >
            <Flame className="mr-2" size={20} />
            Aller vers Coin Chaud
          </Button>
        </div>

        {/* Info card en haut */}
        <Card className="absolute top-4 left-4 right-4 p-3 bg-card/95 backdrop-blur-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <MapPin size={16} className="text-primary" />
              <span className="text-sm font-medium">Zone actuelle: Marcory</span>
            </div>
            <div className="flex items-center space-x-1">
              <Zap size={14} className="text-warning" />
              <span className="text-xs text-muted-foreground">3 demandes proches</span>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}