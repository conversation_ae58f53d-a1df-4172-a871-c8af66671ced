// Driver service for managing driver profiles and onboarding data
// This is a mock implementation for demo purposes

interface DriverProfile {
  id: string;
  userId: string;
  firstName: string;
  lastName: string;
  phone: string;
  profileImage?: string;
  
  // Vehicle Information
  vehicleMake: string;
  vehicleModel: string;
  vehicleYear: number;
  vehicleColor: string;
  licensePlate: string;
  
  // Driver Credentials
  licenseNumber: string;
  licenseExpiry: string;
  
  // Preferences
  preferredZones: string[];
  workingHours: {
    start: string;
    end: string;
  };
  notifications: {
    hotZones: boolean;
    weather: boolean;
    earnings: boolean;
  };
  
  // Statistics
  totalRides: number;
  totalEarnings: number;
  averageRating: number;
  isActive: boolean;
  isOnline: boolean;
  
  createdAt: string;
  updatedAt: string;
}

interface OnboardingData {
  // Personal Information
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  
  // Vehicle Information
  vehicleMake: string;
  vehicleModel: string;
  vehicleYear: number;
  vehicleColor: string;
  licensePlate: string;
  
  // License Information
  licenseNumber: string;
  licenseExpiry: string;
  licensePhoto?: File;
  
  // Profile
  profilePhoto?: File;
  
  // Preferences
  preferredZones: string[];
  workingHours: {
    start: string;
    end: string;
  };
  notifications: {
    hotZones: boolean;
    weather: boolean;
    earnings: boolean;
  };
}

class DriverService {
  private drivers: Map<string, DriverProfile> = new Map();

  async createDriverProfile(userId: string, onboardingData: OnboardingData): Promise<DriverProfile> {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1500));

      const driverProfile: DriverProfile = {
        id: `driver_${Date.now()}`,
        userId,
        firstName: onboardingData.firstName,
        lastName: onboardingData.lastName,
        phone: onboardingData.phone,
        profileImage: onboardingData.profilePhoto ? URL.createObjectURL(onboardingData.profilePhoto) : undefined,
        
        vehicleMake: onboardingData.vehicleMake,
        vehicleModel: onboardingData.vehicleModel,
        vehicleYear: onboardingData.vehicleYear,
        vehicleColor: onboardingData.vehicleColor,
        licensePlate: onboardingData.licensePlate,
        
        licenseNumber: onboardingData.licenseNumber,
        licenseExpiry: onboardingData.licenseExpiry,
        
        preferredZones: onboardingData.preferredZones,
        workingHours: onboardingData.workingHours,
        notifications: onboardingData.notifications,
        
        totalRides: 0,
        totalEarnings: 0,
        averageRating: 0,
        isActive: true,
        isOnline: false,
        
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // Store in memory (in real app, this would be saved to database)
      this.drivers.set(userId, driverProfile);

      return driverProfile;
    } catch (error) {
      console.error('Error creating driver profile:', error);
      throw new Error('Impossible de créer le profil chauffeur');
    }
  }

  async getDriverProfile(userId: string): Promise<DriverProfile | null> {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      return this.drivers.get(userId) || null;
    } catch (error) {
      console.error('Error fetching driver profile:', error);
      return null;
    }
  }

  async updateDriverProfile(userId: string, updates: Partial<DriverProfile>): Promise<DriverProfile> {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      const existingProfile = this.drivers.get(userId);
      if (!existingProfile) {
        throw new Error('Profil chauffeur non trouvé');
      }

      const updatedProfile: DriverProfile = {
        ...existingProfile,
        ...updates,
        updatedAt: new Date().toISOString()
      };

      this.drivers.set(userId, updatedProfile);
      return updatedProfile;
    } catch (error) {
      console.error('Error updating driver profile:', error);
      throw new Error('Impossible de mettre à jour le profil');
    }
  }

  async updateDriverStatus(userId: string, isOnline: boolean): Promise<void> {
    try {
      const profile = this.drivers.get(userId);
      if (profile) {
        profile.isOnline = isOnline;
        profile.updatedAt = new Date().toISOString();
        this.drivers.set(userId, profile);
      }
    } catch (error) {
      console.error('Error updating driver status:', error);
    }
  }

  async getDriverStats(userId: string): Promise<{
    totalRides: number;
    totalEarnings: number;
    averageRating: number;
    hoursWorked: number;
  } | null> {
    try {
      const profile = this.drivers.get(userId);
      if (!profile) return null;

      // Simulate some stats calculation
      return {
        totalRides: profile.totalRides,
        totalEarnings: profile.totalEarnings,
        averageRating: profile.averageRating,
        hoursWorked: Math.floor(Math.random() * 40) + 20 // Mock hours
      };
    } catch (error) {
      console.error('Error fetching driver stats:', error);
      return null;
    }
  }

  // Initialize with demo data
  initializeDemoDriver() {
    const demoProfile: DriverProfile = {
      id: 'demo_driver_1',
      userId: '1',
      firstName: 'Jean',
      lastName: 'Kouassi',
      phone: '+225 07 12 34 56 78',
      profileImage: undefined,
      
      vehicleMake: 'Toyota',
      vehicleModel: 'Yaris',
      vehicleYear: 2020,
      vehicleColor: 'Blanc',
      licensePlate: 'AB 1234 CD',
      
      licenseNumber: 'CI123456789',
      licenseExpiry: '2026-12-31',
      
      preferredZones: ['Plateau', 'Cocody', 'Zone 4'],
      workingHours: {
        start: '07:00',
        end: '19:00'
      },
      notifications: {
        hotZones: true,
        weather: true,
        earnings: true
      },
      
      totalRides: 247,
      totalEarnings: 1250000,
      averageRating: 4.8,
      isActive: true,
      isOnline: true,
      
      createdAt: '2024-01-15T08:00:00Z',
      updatedAt: new Date().toISOString()
    };

    this.drivers.set('1', demoProfile);
  }
}

export const driverService = new DriverService();
export type { DriverProfile, OnboardingData };
