import { useState, useEffect } from "react";
import { useSession } from "@/lib/mockAuth";
import { driverService } from "@/services/driverService";
import WelcomeScreen from "../WelcomeScreen";
import LoginScreen from "./LoginScreen";
import SignupScreen from "./SignupScreen";
import OnboardingFlow from "../onboarding/OnboardingFlow";
import { Loader2, Car } from "lucide-react";

interface AuthWrapperProps {
  children: React.ReactNode;
}

type AuthState = 'loading' | 'login' | 'signup' | 'onboarding' | 'authenticated';

export default function AuthWrapper({ children }: AuthWrapperProps) {
  const { data: session, status } = useSession();
  const [authState, setAuthState] = useState<AuthState>('loading');
  const [signupData, setSignupData] = useState<any>(null);

  useEffect(() => {
    if (status === 'loading') {
      setAuthState('loading');
    } else if (status === 'unauthenticated') {
      setAuthState('welcome');
    } else if (status === 'authenticated') {
      // Check if user has completed onboarding
      if (session?.user?.driver) {
        setAuthState('authenticated');
      } else {
        // User is authenticated but hasn't completed driver onboarding
        setAuthState('onboarding');
      }
    }
  }, [status, session]);

  const handleSwitchToSignup = () => {
    setAuthState('signup');
  };

  const handleSwitchToLogin = () => {
    setAuthState('login');
  };

  const handleSignupSuccess = (userData: any) => {
    setSignupData(userData);
    setAuthState('onboarding');
  };

  const handleGetStarted = () => {
    setAuthState('login');
  };

  const handleOnboardingComplete = (driverProfile: any) => {
    // Update the session with driver profile
    console.log('Onboarding completed for driver:', driverProfile);
    setAuthState('authenticated');
  };

  const handleForgotPassword = () => {
    // TODO: Implement forgot password flow
    console.log("Forgot password clicked");
  };

  if (authState === 'loading') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary/10 to-secondary/10 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
            <Car size={32} className="text-primary-foreground" />
          </div>
          <h1 className="text-2xl font-bold mb-2">ChauffeurX</h1>
          <div className="flex items-center space-x-2">
            <Loader2 className="w-4 h-4 animate-spin" />
            <span className="text-muted-foreground">Chargement...</span>
          </div>
        </div>
      </div>
    );
  }

  if (authState === 'welcome') {
    return <WelcomeScreen onGetStarted={handleGetStarted} />;
  }

  if (authState === 'login') {
    return (
      <LoginScreen
        onSwitchToSignup={handleSwitchToSignup}
        onForgotPassword={handleForgotPassword}
      />
    );
  }

  if (authState === 'signup') {
    return (
      <SignupScreen
        onSwitchToLogin={handleSwitchToLogin}
        onSignupSuccess={handleSignupSuccess}
      />
    );
  }

  if (authState === 'onboarding') {
    const userData = signupData || {
      firstName: session?.user?.name?.split(' ')[0] || 'Chauffeur',
      lastName: session?.user?.name?.split(' ')[1] || 'VTC',
      email: session?.user?.email || '',
      phone: ''
    };

    return (
      <OnboardingFlow
        userData={userData}
        userId={session?.user?.id || 'demo-user'}
        onComplete={handleOnboardingComplete}
      />
    );
  }

  // User is authenticated and has completed onboarding
  return <>{children}</>;
}
