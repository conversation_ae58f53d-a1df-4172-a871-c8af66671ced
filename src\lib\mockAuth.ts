// Mock authentication for demo purposes
// In a real Next.js app, this would be handled by NextAuth API routes

interface User {
  id: string;
  email: string;
  name: string;
  image?: string;
  driver?: any;
}

interface Session {
  user: User;
  expires: string;
}

class MockAuthProvider {
  private currentUser: User | null = null;
  private listeners: ((session: Session | null) => void)[] = [];

  // Demo users
  private demoUsers: User[] = [
    {
      id: '1',
      email: '<EMAIL>',
      name: '<PERSON>',
      image: null,
      driver: {
        id: '1',
        firstName: '<PERSON>',
        lastName: '<PERSON><PERSON><PERSON>',
        phone: '+225 07 12 34 56 78',
        vehicleMake: 'Toyota',
        vehicleModel: 'Yaris',
        vehicleYear: 2020,
        vehicleColor: 'Blanc',
        licensePlate: 'AB 1234 CD',
        totalRides: 247,
        totalEarnings: 1250000,
        averageRating: 4.8,
        isActive: true,
        isOnline: true
      }
    }
  ];

  async signIn(credentials: { email: string; password: string }) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Demo login
    if (credentials.email === '<EMAIL>' && credentials.password === 'chauffeur123') {
      this.currentUser = this.demoUsers[0];
      this.notifyListeners();
      return { ok: true, error: null };
    }

    return { ok: false, error: 'Invalid credentials' };
  }

  async signOut() {
    this.currentUser = null;
    this.notifyListeners();
  }

  getSession(): Session | null {
    if (!this.currentUser) return null;

    return {
      user: this.currentUser,
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours
    };
  }

  onSessionChange(callback: (session: Session | null) => void) {
    this.listeners.push(callback);
    
    // Return unsubscribe function
    return () => {
      this.listeners = this.listeners.filter(listener => listener !== callback);
    };
  }

  private notifyListeners() {
    const session = this.getSession();
    this.listeners.forEach(listener => listener(session));
  }

  // Initialize with demo user for development
  initDemo() {
    // Initialize demo driver data
    const { driverService } = require('@/services/driverService');
    driverService.initializeDemoDriver();

    this.currentUser = this.demoUsers[0];
    this.notifyListeners();
  }
}

export const mockAuth = new MockAuthProvider();

// Mock useSession hook
export function useSession() {
  const [session, setSession] = useState<Session | null>(mockAuth.getSession());
  const [status, setStatus] = useState<'loading' | 'authenticated' | 'unauthenticated'>(
    mockAuth.getSession() ? 'authenticated' : 'unauthenticated'
  );

  useEffect(() => {
    const unsubscribe = mockAuth.onSessionChange((newSession) => {
      setSession(newSession);
      setStatus(newSession ? 'authenticated' : 'unauthenticated');
    });

    return unsubscribe;
  }, []);

  return { data: session, status };
}

// Mock signIn function
export async function signIn(provider: string, credentials?: any) {
  if (provider === 'credentials' && credentials) {
    return await mockAuth.signIn(credentials);
  }
  
  if (provider === 'google') {
    // Simulate Google sign in
    await new Promise(resolve => setTimeout(resolve, 1000));
    mockAuth.initDemo();
    return { ok: true, error: null };
  }

  return { ok: false, error: 'Provider not supported' };
}

// Mock signOut function
export async function signOut() {
  await mockAuth.signOut();
}

import { useState, useEffect } from 'react';

// Mock SessionProvider
export function SessionProvider({ children }: { children: React.ReactNode }) {
  return children;
}
