import { useEffect, useState } from 'react';
import { Button } from './ui/button';
import { Car, Users, MapPin, Zap } from 'lucide-react';

interface WelcomeScreenProps {
  onGetStarted: () => void;
}

export default function WelcomeScreen({ onGetStarted }: WelcomeScreenProps) {
  const [animationStep, setAnimationStep] = useState(0);

  useEffect(() => {
    const timer = setInterval(() => {
      setAnimationStep(prev => (prev + 1) % 4);
    }, 2000);

    return () => clearInterval(timer);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/10 via-background to-secondary/10 flex flex-col items-center justify-center p-4 overflow-hidden">
      
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Moving clouds */}
        <div className="absolute top-20 left-0 w-32 h-16 bg-white/20 rounded-full animate-pulse" 
             style={{ animation: 'float 20s infinite linear' }}></div>
        <div className="absolute top-32 right-0 w-24 h-12 bg-white/15 rounded-full" 
             style={{ animation: 'float 25s infinite linear reverse' }}></div>
        
        {/* City skyline */}
        <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-gray-200/30 to-transparent">
          <div className="absolute bottom-0 left-1/4 w-8 h-20 bg-gray-300/40"></div>
          <div className="absolute bottom-0 left-1/3 w-6 h-16 bg-gray-300/40"></div>
          <div className="absolute bottom-0 left-1/2 w-10 h-24 bg-gray-300/40"></div>
          <div className="absolute bottom-0 right-1/3 w-7 h-18 bg-gray-300/40"></div>
          <div className="absolute bottom-0 right-1/4 w-9 h-22 bg-gray-300/40"></div>
        </div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 text-center max-w-md mx-auto">
        
        {/* Logo and Title */}
        <div className="mb-8">
          <div className="w-20 h-20 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
            <Car className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-4xl font-bold text-foreground mb-2">ChauffeurX</h1>
          <p className="text-lg text-muted-foreground">Votre partenaire VTC à Abidjan</p>
        </div>

        {/* Animated Car Scene */}
        <div className="relative h-40 mb-8 bg-gradient-to-r from-blue-100 to-green-100 rounded-2xl overflow-hidden shadow-inner">
          
          {/* Road */}
          <div className="absolute bottom-0 left-0 right-0 h-16 bg-gray-400">
            <div className="absolute top-1/2 left-0 right-0 h-1 bg-white opacity-60"
                 style={{ 
                   backgroundImage: 'repeating-linear-gradient(to right, white 0, white 20px, transparent 20px, transparent 40px)',
                   animation: 'roadLines 2s infinite linear'
                 }}></div>
          </div>

          {/* Animated Car */}
          <div className="absolute bottom-16 transition-all duration-2000 ease-in-out"
               style={{ 
                 left: `${animationStep * 25}%`,
                 transform: animationStep === 3 ? 'scale(1.1)' : 'scale(1)'
               }}>
            <div className="relative">
              {/* Car body */}
              <div className="w-16 h-8 bg-gradient-to-r from-primary to-primary/80 rounded-lg relative shadow-lg">
                {/* Windows */}
                <div className="absolute top-1 left-2 right-2 h-3 bg-blue-200 rounded opacity-80"></div>
                {/* Wheels */}
                <div className="absolute -bottom-1 left-1 w-3 h-3 bg-gray-800 rounded-full"></div>
                <div className="absolute -bottom-1 right-1 w-3 h-3 bg-gray-800 rounded-full"></div>
                {/* Headlights */}
                <div className="absolute top-2 -right-1 w-1 h-2 bg-yellow-300 rounded-full"></div>
              </div>
              
              {/* Passenger indicator */}
              {animationStep >= 2 && (
                <div className="absolute -top-6 left-1/2 transform -translate-x-1/2">
                  <Users className="w-4 h-4 text-green-500 animate-bounce" />
                </div>
              )}
            </div>
          </div>

          {/* Pickup/Dropoff Points */}
          <div className="absolute bottom-20 left-4">
            <MapPin className={`w-6 h-6 transition-colors duration-500 ${
              animationStep === 0 ? 'text-green-500' : 'text-gray-400'
            }`} />
            <span className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-xs text-gray-600">
              Départ
            </span>
          </div>
          
          <div className="absolute bottom-20 right-4">
            <MapPin className={`w-6 h-6 transition-colors duration-500 ${
              animationStep === 3 ? 'text-red-500' : 'text-gray-400'
            }`} />
            <span className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-xs text-gray-600">
              Arrivée
            </span>
          </div>

          {/* Status Text */}
          <div className="absolute top-4 left-1/2 transform -translate-x-1/2">
            <div className="bg-white/90 px-3 py-1 rounded-full text-sm font-medium">
              {animationStep === 0 && "🚗 En attente du passager"}
              {animationStep === 1 && "👋 Passager à bord"}
              {animationStep === 2 && "🛣️ Course en cours"}
              {animationStep === 3 && "✅ Destination atteinte"}
            </div>
          </div>
        </div>

        {/* Features */}
        <div className="grid grid-cols-3 gap-4 mb-8">
          <div className="text-center">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-2">
              <Zap className="w-6 h-6 text-primary" />
            </div>
            <p className="text-sm text-muted-foreground">IA Intelligente</p>
          </div>
          <div className="text-center">
            <div className="w-12 h-12 bg-secondary/10 rounded-full flex items-center justify-center mx-auto mb-2">
              <MapPin className="w-6 h-6 text-secondary" />
            </div>
            <p className="text-sm text-muted-foreground">Zones Chaudes</p>
          </div>
          <div className="text-center">
            <div className="w-12 h-12 bg-green-500/10 rounded-full flex items-center justify-center mx-auto mb-2">
              <Users className="w-6 h-6 text-green-500" />
            </div>
            <p className="text-sm text-muted-foreground">Plus de Clients</p>
          </div>
        </div>

        {/* Call to Action */}
        <div className="space-y-4">
          <Button 
            onClick={onGetStarted}
            size="lg"
            className="w-full bg-gradient-to-r from-primary to-secondary hover:from-primary/90 hover:to-secondary/90 text-white font-semibold py-4 text-lg shadow-lg"
          >
            🚀 Commencer Maintenant
          </Button>
          
          <p className="text-sm text-muted-foreground">
            Rejoignez des milliers de chauffeurs VTC à Abidjan
          </p>
        </div>
      </div>

      {/* Custom CSS for animations */}
      <style jsx>{`
        @keyframes float {
          from { transform: translateX(-100px); }
          to { transform: translateX(calc(100vw + 100px)); }
        }
        
        @keyframes roadLines {
          from { transform: translateX(0); }
          to { transform: translateX(40px); }
        }
      `}</style>
    </div>
  );
}
