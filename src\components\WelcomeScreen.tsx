import { useEffect, useState } from 'react';
import { Button } from './ui/button';
import { Car, Users, MapPin, Zap } from 'lucide-react';

interface WelcomeScreenProps {
  onGetStarted: () => void;
}

export default function WelcomeScreen({ onGetStarted }: WelcomeScreenProps) {
  const [animationStep, setAnimationStep] = useState(0);

  useEffect(() => {
    const timer = setInterval(() => {
      setAnimationStep(prev => (prev + 1) % 4);
    }, 2000);

    return () => clearInterval(timer);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-yellow-50 via-white to-green-50 flex flex-col items-center justify-center p-6 overflow-hidden relative">

      {/* Modern Background Pattern */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Geometric shapes */}
        <div className="absolute top-20 left-10 w-20 h-20 bg-yellow-200/20 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute top-40 right-16 w-32 h-32 bg-green-200/20 rounded-full blur-2xl"
             style={{ animation: 'float 15s infinite ease-in-out' }}></div>
        <div className="absolute bottom-40 left-20 w-24 h-24 bg-blue-200/20 rounded-full blur-xl"
             style={{ animation: 'float 20s infinite ease-in-out reverse' }}></div>

        {/* Modern grid pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="grid grid-cols-8 grid-rows-12 h-full w-full">
            {Array.from({ length: 96 }).map((_, i) => (
              <div key={i} className="border border-gray-400"></div>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 text-center max-w-sm mx-auto w-full">

        {/* Modern Logo and Title */}
        <div className="mb-10">
          <div className="relative mx-auto mb-6">
            <div className="w-24 h-24 bg-gradient-to-br from-yellow-400 via-yellow-500 to-orange-500 rounded-3xl flex items-center justify-center shadow-2xl border-4 border-white/50 backdrop-blur-sm">
              <Car className="w-12 h-12 text-white drop-shadow-lg" />
            </div>
            {/* Floating elements around logo */}
            <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-400 rounded-full animate-bounce shadow-lg"></div>
            <div className="absolute -bottom-1 -left-1 w-4 h-4 bg-blue-400 rounded-full animate-pulse shadow-lg"></div>
          </div>
          <h1 className="text-5xl font-black text-transparent bg-clip-text bg-gradient-to-r from-yellow-600 via-orange-600 to-red-600 mb-3 tracking-tight">
            ChauffeurX
          </h1>
          <p className="text-xl text-gray-600 font-medium">Votre partenaire VTC à Abidjan</p>
          <div className="w-16 h-1 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full mx-auto mt-3"></div>
        </div>

        {/* Animated Taxi Scene */}
        <div className="relative h-48 mb-8 bg-gradient-to-r from-blue-50 via-yellow-50 to-green-50 rounded-3xl overflow-hidden shadow-2xl border border-yellow-200/50">

          {/* City Background */}
          <div className="absolute inset-0 bg-gradient-to-b from-sky-200/30 to-transparent">
            {/* Buildings */}
            <div className="absolute bottom-20 left-8 w-6 h-12 bg-gray-300/40 rounded-t"></div>
            <div className="absolute bottom-20 left-16 w-4 h-8 bg-gray-300/40 rounded-t"></div>
            <div className="absolute bottom-20 right-12 w-8 h-16 bg-gray-300/40 rounded-t"></div>
            <div className="absolute bottom-20 right-4 w-5 h-10 bg-gray-300/40 rounded-t"></div>
          </div>

          {/* Road */}
          <div className="absolute bottom-0 left-0 right-0 h-20 bg-gradient-to-t from-gray-500 to-gray-400">
            {/* Road markings */}
            <div className="absolute top-2 left-0 right-0 h-1 bg-white opacity-70"
                 style={{
                   backgroundImage: 'repeating-linear-gradient(to right, white 0, white 25px, transparent 25px, transparent 50px)',
                   animation: 'roadLines 1.5s infinite linear'
                 }}></div>
            <div className="absolute bottom-2 left-0 right-0 h-1 bg-yellow-300 opacity-80"
                 style={{
                   backgroundImage: 'repeating-linear-gradient(to right, #fcd34d 0, #fcd34d 15px, transparent 15px, transparent 30px)',
                   animation: 'roadLines 1.5s infinite linear'
                 }}></div>
          </div>

          {/* Animated Taxi */}
          <div className="absolute bottom-20 transition-all duration-2000 ease-in-out"
               style={{
                 left: `${animationStep * 22 + 5}%`,
                 transform: animationStep === 3 ? 'scale(1.05)' : 'scale(1)'
               }}>
            <div className="relative">
              {/* Taxi body */}
              <div className="w-20 h-10 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-lg relative shadow-xl border-2 border-yellow-600/30">
                {/* Taxi roof sign */}
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 w-8 h-3 bg-yellow-300 rounded-sm border border-yellow-600 shadow-md">
                  <div className="absolute inset-0 bg-gradient-to-r from-yellow-200 to-yellow-300 rounded-sm flex items-center justify-center">
                    <span className="text-xs font-bold text-yellow-800">TAXI</span>
                  </div>
                </div>

                {/* Windows */}
                <div className="absolute top-1 left-3 right-3 h-4 bg-gradient-to-b from-blue-200 to-blue-300 rounded opacity-90 border border-blue-400/30"></div>

                {/* Door lines */}
                <div className="absolute top-2 left-1/2 w-0.5 h-6 bg-yellow-600/40"></div>

                {/* Wheels */}
                <div className="absolute -bottom-1 left-2 w-4 h-4 bg-gray-800 rounded-full border-2 border-gray-600">
                  <div className="absolute inset-1 bg-gray-300 rounded-full"></div>
                </div>
                <div className="absolute -bottom-1 right-2 w-4 h-4 bg-gray-800 rounded-full border-2 border-gray-600">
                  <div className="absolute inset-1 bg-gray-300 rounded-full"></div>
                </div>

                {/* Headlights */}
                <div className="absolute top-3 -right-1 w-2 h-3 bg-yellow-100 rounded-full border border-yellow-300 shadow-sm"></div>

                {/* Side mirror */}
                <div className="absolute top-2 -left-1 w-1 h-1 bg-gray-600 rounded-full"></div>

                {/* Taxi stripes */}
                <div className="absolute top-6 left-1 right-1 h-1 bg-yellow-600/60 rounded"></div>
              </div>

              {/* Passenger indicator */}
              {animationStep >= 2 && (
                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2">
                  <div className="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium shadow-lg animate-bounce">
                    <Users className="w-3 h-3 inline mr-1" />
                    Client
                  </div>
                </div>
              )}

              {/* Taxi meter indicator */}
              {animationStep >= 1 && (
                <div className="absolute -top-2 right-0 w-2 h-2 bg-green-400 rounded-full animate-pulse shadow-lg"></div>
              )}
            </div>
          </div>

          {/* Pickup/Dropoff Points */}
          <div className="absolute bottom-20 left-4">
            <MapPin className={`w-6 h-6 transition-colors duration-500 ${
              animationStep === 0 ? 'text-green-500' : 'text-gray-400'
            }`} />
            <span className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-xs text-gray-600">
              Départ
            </span>
          </div>
          
          <div className="absolute bottom-20 right-4">
            <MapPin className={`w-6 h-6 transition-colors duration-500 ${
              animationStep === 3 ? 'text-red-500' : 'text-gray-400'
            }`} />
            <span className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-xs text-gray-600">
              Arrivée
            </span>
          </div>

          {/* Status Text */}
          <div className="absolute top-4 left-1/2 transform -translate-x-1/2">
            <div className="bg-white/95 backdrop-blur-sm px-4 py-2 rounded-full text-sm font-semibold shadow-lg border border-yellow-200">
              {animationStep === 0 && "🚖 Taxi disponible"}
              {animationStep === 1 && "👋 Client monte à bord"}
              {animationStep === 2 && "🛣️ Course VTC en cours"}
              {animationStep === 3 && "✅ Arrivée à destination"}
            </div>
          </div>
        </div>

        {/* Modern Features */}
        <div className="grid grid-cols-3 gap-3 mb-10">
          <div className="text-center group">
            <div className="w-16 h-16 bg-gradient-to-br from-yellow-100 to-yellow-200 rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-lg group-hover:scale-105 transition-transform duration-300 border border-yellow-300/30">
              <Zap className="w-8 h-8 text-yellow-600" />
            </div>
            <p className="text-sm font-semibold text-gray-700">IA Intelligente</p>
          </div>
          <div className="text-center group">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-blue-200 rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-lg group-hover:scale-105 transition-transform duration-300 border border-blue-300/30">
              <MapPin className="w-8 h-8 text-blue-600" />
            </div>
            <p className="text-sm font-semibold text-gray-700">Zones Chaudes</p>
          </div>
          <div className="text-center group">
            <div className="w-16 h-16 bg-gradient-to-br from-green-100 to-green-200 rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-lg group-hover:scale-105 transition-transform duration-300 border border-green-300/30">
              <Users className="w-8 h-8 text-green-600" />
            </div>
            <p className="text-sm font-semibold text-gray-700">Plus de Clients</p>
          </div>
        </div>

        {/* Modern Call to Action */}
        <div className="space-y-6">
          <Button
            onClick={onGetStarted}
            size="lg"
            className="w-full h-16 bg-gradient-to-r from-yellow-500 via-orange-500 to-red-500 hover:from-yellow-600 hover:via-orange-600 hover:to-red-600 text-white font-bold text-xl shadow-2xl rounded-2xl border-2 border-white/20 backdrop-blur-sm transform hover:scale-105 transition-all duration-300 active:scale-95"
          >
            <span className="flex items-center justify-center space-x-3">
              <span>🚀</span>
              <span>Commencer Maintenant</span>
            </span>
          </Button>

          <div className="text-center">
            <p className="text-base font-medium text-gray-600 mb-2">
              Rejoignez des milliers de chauffeurs VTC
            </p>
            <p className="text-sm text-gray-500">
              ⭐ Plus de revenus • 📱 Interface moderne • 🤖 IA avancée
            </p>
          </div>
        </div>
      </div>

      {/* Custom CSS for animations */}
      <style jsx>{`
        @keyframes float {
          from { transform: translateX(-100px); }
          to { transform: translateX(calc(100vw + 100px)); }
        }
        
        @keyframes roadLines {
          from { transform: translateX(0); }
          to { transform: translateX(40px); }
        }
      `}</style>
    </div>
  );
}
