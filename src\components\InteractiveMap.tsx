import { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Circle, useMap } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { HotZone, Coordinates } from '@/services/mapService';
import { Button } from './ui/button';
import { Navigation, Flame, MapPin } from 'lucide-react';

// Fix for default markers in react-leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Custom icons for different zone intensities
const createHotZoneIcon = (intensity: string) => {
  const color = intensity === 'Très chaud' ? '#ff4444' : 
                intensity === 'Chaud' ? '#ff8800' : '#32cd32';
  
  return L.divIcon({
    className: 'custom-hot-zone-marker',
    html: `
      <div style="
        background-color: ${color};
        width: 30px;
        height: 30px;
        border-radius: 50%;
        border: 3px solid white;
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        animation: ${intensity === 'Très chaud' ? 'pulse 2s infinite' : 'none'};
      ">
        <span style="color: white; font-size: 16px;">🔥</span>
      </div>
      <style>
        @keyframes pulse {
          0% { transform: scale(1); opacity: 1; }
          50% { transform: scale(1.1); opacity: 0.8; }
          100% { transform: scale(1); opacity: 1; }
        }
      </style>
    `,
    iconSize: [30, 30],
    iconAnchor: [15, 15],
  });
};

const driverIcon = L.divIcon({
  className: 'custom-driver-marker',
  html: `
    <div style="
      background-color: #007AFF;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      border: 3px solid white;
      box-shadow: 0 2px 8px rgba(0,0,0,0.3);
      animation: bounce 2s infinite;
    "></div>
    <style>
      @keyframes bounce {
        0%, 100% { transform: translateY(0); }
        50% { transform: translateY(-4px); }
      }
    </style>
  `,
  iconSize: [20, 20],
  iconAnchor: [10, 10],
});

interface InteractiveMapProps {
  hotZones: HotZone[];
  currentLocation: Coordinates | null;
  onZoneClick: (zone: HotZone) => void;
  onNavigateToZone: (zone: HotZone) => void;
  className?: string;
}

// Component to handle map events and updates
function MapController({ currentLocation }: { currentLocation: Coordinates | null }) {
  const map = useMap();
  
  useEffect(() => {
    if (currentLocation) {
      map.setView([currentLocation.lat, currentLocation.lng], map.getZoom());
    }
  }, [currentLocation, map]);
  
  return null;
}

export default function InteractiveMap({ 
  hotZones, 
  currentLocation, 
  onZoneClick, 
  onNavigateToZone,
  className = ""
}: InteractiveMapProps) {
  const [mapReady, setMapReady] = useState(false);
  
  // Abidjan center coordinates
  const abidjanCenter: [number, number] = [5.3600, -4.0083];
  
  useEffect(() => {
    setMapReady(true);
  }, []);

  if (!mapReady) {
    return (
      <div className={`flex items-center justify-center bg-muted/30 ${className}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
          <p className="text-sm text-muted-foreground">Chargement de la carte...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      <MapContainer
        center={currentLocation ? [currentLocation.lat, currentLocation.lng] : abidjanCenter}
        zoom={12}
        style={{ height: '100%', width: '100%' }}
        className="rounded-lg"
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />
        
        <MapController currentLocation={currentLocation} />
        
        {/* Driver's current location */}
        {currentLocation && (
          <Marker
            position={[currentLocation.lat, currentLocation.lng]}
            icon={driverIcon}
          >
            <Popup>
              <div className="text-center p-2">
                <MapPin className="w-4 h-4 mx-auto mb-1 text-primary" />
                <p className="font-semibold">Votre position</p>
                <p className="text-xs text-muted-foreground">
                  {currentLocation.lat.toFixed(4)}, {currentLocation.lng.toFixed(4)}
                </p>
              </div>
            </Popup>
          </Marker>
        )}
        
        {/* Hot zones */}
        {hotZones.map((zone) => (
          <div key={zone.id}>
            {/* Zone marker */}
            <Marker
              position={[zone.coordinates.lat, zone.coordinates.lng]}
              icon={createHotZoneIcon(zone.intensity)}
              eventHandlers={{
                click: () => onZoneClick(zone),
              }}
            >
              <Popup>
                <div className="p-3 min-w-[200px]">
                  <div className="flex items-center space-x-2 mb-2">
                    <Flame className="w-4 h-4 text-orange-500" />
                    <h3 className="font-semibold">{zone.name}</h3>
                  </div>
                  
                  <div className="space-y-1 text-sm mb-3">
                    <p><span className="font-medium">Demande:</span> {zone.demandLevel}%</p>
                    <p><span className="font-medium">Attente:</span> {zone.estimatedWaitTime} min</p>
                    <p><span className="font-medium">Revenu moyen:</span> {zone.averageRideValue.toLocaleString()} CFA</p>
                    <p><span className="font-medium">Intensité:</span> 
                      <span className={`ml-1 px-2 py-1 rounded text-xs ${
                        zone.intensity === 'Très chaud' ? 'bg-red-100 text-red-800' :
                        zone.intensity === 'Chaud' ? 'bg-orange-100 text-orange-800' :
                        'bg-green-100 text-green-800'
                      }`}>
                        {zone.intensity}
                      </span>
                    </p>
                  </div>
                  
                  <Button
                    size="sm"
                    onClick={() => onNavigateToZone(zone)}
                    className="w-full"
                  >
                    <Navigation className="w-3 h-3 mr-1" />
                    Naviguer
                  </Button>
                </div>
              </Popup>
            </Marker>
            
            {/* Zone radius circle */}
            <Circle
              center={[zone.coordinates.lat, zone.coordinates.lng]}
              radius={500} // 500 meters radius
              pathOptions={{
                color: zone.intensity === 'Très chaud' ? '#ff4444' : 
                       zone.intensity === 'Chaud' ? '#ff8800' : '#32cd32',
                fillColor: zone.intensity === 'Très chaud' ? '#ff4444' : 
                          zone.intensity === 'Chaud' ? '#ff8800' : '#32cd32',
                fillOpacity: 0.1,
                weight: 2,
              }}
            />
          </div>
        ))}
      </MapContainer>
      
      {/* Map legend */}
      <div className="absolute bottom-4 left-4 bg-card/95 backdrop-blur-sm rounded-lg p-3 shadow-lg">
        <h4 className="font-semibold text-sm mb-2">Légende</h4>
        <div className="space-y-1 text-xs">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
            <span>Très chaud (80%+)</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
            <span>Chaud (65-79%)</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <span>Modéré (&lt;65%)</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
            <span>Votre position</span>
          </div>
        </div>
      </div>
    </div>
  );
}
